{"name": "cubecraft", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "npm run generate:sitemap && vite build", "lint": "eslint .", "preview": "vite preview", "generate:sitemap": "node scripts/generateSitemap.js"}, "dependencies": {"react": "^18.3.1", "react-dom": "^18.3.1", "react-ga4": "^2.1.0", "react-helmet": "^6.1.0", "react-helmet-async": "^2.0.5", "react-router-dom": "^7.0.1", "uuid": "^11.0.3", "xml2js": "^0.6.2"}, "devDependencies": {"@eslint/js": "^9.15.0", "@types/node": "^22.13.2", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/xml2js": "^0.4.14", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.15.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.12.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.15", "ts-node": "^10.9.2", "typescript": "^5.7.3", "vite": "^6.0.1"}}