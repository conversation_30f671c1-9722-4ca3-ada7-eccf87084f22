# PaperCrafter Documentation

## Table of Contents

1. [Project Structure](#project-structure)
2. [Components](#components)
3. [Styling](#styling)
4. [Print Functionality](#print-functionality)
5. [State Management](#state-management)
6. [Responsive Design](#responsive-design)
7. [Analytics](#analytics)

## Project Structure

```
CubeCraft/
├── src/
│   ├── components/         # Reusable React components
│   ├── pages/             # Page components
│   ├── utils/             # Utility functions and data
│   ├── assets/            # Static assets
│   ├── hooks/             # Custom React hooks
│   └── styles/            # CSS and style files
├── public/                # Public assets
└── docs/                  # Documentation
```

## Components

### UnfoldedCube

The core component for rendering papercraft templates.

```jsx
Props:
- faces: Object containing textures for each face
- onFaceClick: Function to handle face selection
- onClearFace: Function to clear a face
- size: Number for cube size
- containerSize: Number for container dimensions
```

### Accordion

Collapsible component for displaying "How It Works" section.

```jsx
Props:
- title: String for accordion header
- children: React nodes for accordion content
```

### Sidebar

Texture selection panel with search functionality.

```jsx
Props:
- blocks: Array of available textures
- selectedTexture: Currently selected texture
- handleTextureSelect: Selection handler
- searchQuery: Search input value
- setSearchQuery: Search update function
- isOpen: Sidebar visibility state
- setIsOpen: Sidebar visibility handler
```

## Styling

The project uses a combination of Tailwind CSS and custom CSS files:

- `minecraft-style.css`: Minecraft-themed styling
- `print.css`: Print-specific styles
- `index.css`: Global styles
- Tailwind classes for responsive design

### Print Styles

```css
@media print {
  @page {
    size: A4 portrait;
    margin: 0;
  }
  #root {
    width: fit-content !important;
  }
}
```

## Print Functionality

Print detection and handling is managed through the `usePrintMedia` hook:

```javascript
const usePrintMedia = () => {
  // Detects print media state
  // Handles different browser implementations
  // Returns current printing state
};
```

Print workflow:

1. User clicks "Print Templates"
2. Size adjusts for optimal printing
3. Print dialog opens
4. Template renders in print-friendly format

## State Management

Main state objects in App.jsx:

```javascript
- selectedTexture: Currently selected block texture
- cube1Faces/cube2Faces: Texture data for each cube
- size: Current template size
- searchQuery: Texture search input
- isSidebarOpen: Sidebar visibility state
- isPrinting: Print mode state
```

## Responsive Design

The application uses a mobile-first approach with breakpoints:

- Mobile: Default styles
- Tablet (md): > 768px
- Desktop (lg): > 1024px

Key responsive features:

- Collapsible sidebar on mobile
- Adaptive template sizing
- Flexible layout for different screen sizes

## Analytics

Google Analytics integration:

```javascript
// Initialize GA4
ReactGA.initialize("G-CGoogleAnalyticsID"); // Replace with your actual GA4 ID

// Page view tracking
const PageTracker = () => {
  const location = useLocation();
  useEffect(() => {
    ReactGA.send({ hitType: "pageview", page: location.pathname });
  }, [location]);
  return null;
};
```

## Browser Compatibility

The application supports:

- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile browsers
- Print functionality varies by browser:
  - Desktop: Direct printing
  - Mobile: Browser-dependent print dialog

## Performance Considerations

1. Image Optimization

   - Texture images are optimized for web
   - Lazy loading for texture gallery

2. Print Optimization

   - Dedicated print styles
   - Optimized template sizing
   - Hidden UI elements in print view

3. Mobile Performance
   - Responsive images
   - Touch-friendly interface
   - Optimized rendering

## Development Guidelines

1. Code Style

   - Use ESLint configuration
   - Follow Prettier formatting
   - Use TypeScript-style prop definitions

2. Component Structure

   - Functional components with hooks
   - Props documentation
   - Clear component responsibilities

3. Testing
   - Component testing with React Testing Library
   - Browser compatibility testing
   - Print layout testing

## Deployment

The application can be deployed using:

```bash
npm run build
```

Key deployment considerations:

- Environment variables
- Build optimization
- Asset optimization
- Cache policies
