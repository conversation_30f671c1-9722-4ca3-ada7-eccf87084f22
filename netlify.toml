[build]
  publish = "dist"
  command = "npm run build"

# Prerender important pages for better SEO
[build.processing]
  skip_processing = false

[build.processing.css]
  bundle = true
  minify = true

[build.processing.js]
  bundle = true
  minify = true

[build.processing.html]
  pretty_urls = true

# Redirects for SPA
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  force = false

# Redirect old URLs if any
[[redirects]]
  from = "/sitemap_index.xml"
  to = "/sitemap-index.xml"
  status = 301

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' https://www.googletagmanager.com https://www.google-analytics.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https://www.google-analytics.com https://analytics.google.com; font-src 'self' data:;"
    Permissions-Policy = "camera=(), microphone=(), geolocation=()"
    Strict-Transport-Security = "max-age=31536000; includeSubDomains"

# Cache static assets
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Cache images
[[headers]]
  for = "/images/*"
  [headers.values]
    Cache-Control = "public, max-age=2592000"

# Cache sitemaps and robots.txt
[[headers]]
  for = "/sitemap*.xml"
  [headers.values]
    Cache-Control = "public, max-age=86400"
    Content-Type = "application/xml"

[[headers]]
  for = "/robots.txt"
  [headers.values]
    Cache-Control = "public, max-age=86400"
    Content-Type = "text/plain"

# HTML pages - shorter cache for dynamic content
[[headers]]
  for = "/*.html"
  [headers.values]
    Cache-Control = "public, max-age=3600"

