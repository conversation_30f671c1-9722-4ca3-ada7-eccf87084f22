<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0" />
    <meta
      name="robots"
      content="index, follow" />
    <link
      rel="canonical"
      href="https://papercrafter.netlify.app/" />
    <meta
      name="author"
      content="Manu" />
    <title>PaperCrafter - Minecraft Papercraft Template Creator</title>
    <meta
      name="description"
      content="Create and print free Minecraft papercraft templates. Design custom paper blocks and bring your favorite Minecraft blocks into the real world!" />
    <meta
      name="keywords"
      content="minecraft papercraft, minecraft templates, paper minecraft, block templates, minecraft crafts" />
    <!-- Google Analytics -->
    <script
      async
      src="https://www.googletagmanager.com/gtag/js?id=G-C4NP74SX97"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }
      gtag("js", new Date());
      gtag("config", "G-C4NP74SX97");
    </script>

    <!-- Open Graph -->
    <meta
      property="og:type"
      content="website" />
    <meta
      property="og:url"
      content="https://papercrafter.netlify.app/" />
    <meta
      property="og:title"
      content="PaperCrafter - Minecraft Papercraft Template Creator" />
    <meta
      property="og:description"
      content="Create and print free Minecraft papercraft templates. Design custom paper blocks!" />
    <meta
      property="og:image"
      content="https://papercrafter.netlify.app/images/social-preview.png" />

    <!-- Structured Data -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "PaperCrafter",
        "url": "https://papercrafter.netlify.app",
        "description": "Free online tool to create and print Minecraft papercraft templates",
        "applicationCategory": "DesignApplication",
        "offers": {
          "@type": "Offer",
          "price": "0"
        }
      }
    </script>
    <link
      rel="icon"
      type="image/png"
      href="/minecraft_icon.svg" />

    <!-- Preload critical assets -->
    <link
      rel="preload"
      as="image"
      href="/minecraft_icon.svg" />

    <!-- Error handling for missing assets -->
    <script>
      window.addEventListener(
        "error",
        function (e) {
          if (e.target.tagName === "IMG" || e.target.tagName === "SCRIPT") {
            console.error("Resource loading error:", e.target.src);
            // Attempt to reload failed resources
            if (e.target.tagName === "SCRIPT") {
              const script = document.createElement("script");
              script.src = e.target.src;
              script.async = true;
              document.head.appendChild(script);
            }
          }
        },
        true
      );
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script
      type="module"
      src="/src/main.jsx"></script>
    <script
      type="module"
      src="/src/main.jsx"></script>

    <!-- Fallback for JavaScript -->
    <noscript>
      <div style="text-align: center; padding: 20px">
        Please enable JavaScript to use PaperCrafter.
      </div>
    </noscript>
  </body>
</html>
