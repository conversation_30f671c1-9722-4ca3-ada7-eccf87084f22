# PaperCrafter - Minecraft Papercraft Template Creator

PaperCrafter is a web application that allows you to create printable papercraft templates of Minecraft blocks. Built with React and Vite, it features a user-friendly interface for selecting textures and generating printable cube templates.

![PaperCrafter Screenshot](./images/Papercrafter.png)

## Features

- 🎨 Select from hundreds of Minecraft block textures
- 🖨️ Print-optimized layout
- 📱 Responsive design for all devices
- 🎮 Interactive cube face selection
- 🎯 Mobile-friendly interface

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn

### Installation

1. Clone the repository:

```bash
git clone https://github.com/yourusername/CubeCraft.git
cd CubeCraft
```

2. Install dependencies:

```bash
npm install
```

3. Start the development server:

```bash
npm run dev
```

The application will be available at `http://localhost:5173`

## Building for Production

To create a production build:

```bash
npm run build
```

## Technology Stack

- React 18
- Vite
- Tailwind CSS
- React Router
- React Helmet Async
- Google Analytics

## Development Tools

- ESLint for code linting
- Prettier for code formatting
- Vite for fast development and building

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Minecraft textures and designs are property of Mojang AB
- Built with React and Vite
- Styled with Tailwind CSS
🎮 [PaperCrafter](https://papercrafter.netlify.app) - Minecraft Papercraft Template Creator
