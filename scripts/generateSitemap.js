import fs from 'fs';
import path from 'path';
import { articles } from '../src/data/articles.js';

const baseUrl = 'https://papercrafter.netlify.app';

// Static pages with their priorities and change frequencies
const staticPages = [
  {
    url: '/',
    lastmod: '2024-12-20',
    changefreq: 'weekly',
    priority: '1.0'
  },
  {
    url: '/blog',
    lastmod: '2024-12-20',
    changefreq: 'weekly',
    priority: '0.9'
  },
  {
    url: '/contact',
    lastmod: '2024-12-15',
    changefreq: 'monthly',
    priority: '0.7'
  },
  {
    url: '/why-minecraft',
    lastmod: '2024-12-15',
    changefreq: 'monthly',
    priority: '0.8'
  },
  {
    url: '/papercraft-benefits',
    lastmod: '2024-12-15',
    changefreq: 'monthly',
    priority: '0.8'
  },
  {
    url: '/privacy-policy',
    lastmod: '2024-12-10',
    changefreq: 'yearly',
    priority: '0.3'
  },
  {
    url: '/cookie-policy',
    lastmod: '2024-12-10',
    changefreq: 'yearly',
    priority: '0.3'
  }
];

// Generate article pages from articles data
const articlePages = Object.entries(articles).map(([slug, article]) => ({
  url: `/blog/${slug}`,
  lastmod: article.metadata?.dateModified || article.structuredData?.dateModified || '2024-12-20',
  changefreq: 'monthly',
  priority: '0.8'
}));

// Combine all pages
const allPages = [...staticPages, ...articlePages];

// Generate XML sitemap
function generateSitemap() {
  const xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${allPages.map(page => `  <url>
    <loc>${baseUrl}${page.url}</loc>
    <lastmod>${page.lastmod}</lastmod>
    <changefreq>${page.changefreq}</changefreq>
    <priority>${page.priority}</priority>
  </url>`).join('\n')}
</urlset>`;

  return xml;
}

// Generate image sitemap for blog images
function generateImageSitemap() {
  const imageEntries = Object.entries(articles)
    .filter(([_, article]) => article.metadata?.blogListImage)
    .map(([slug, article]) => {
      const imageUrl = article.metadata.blogListImage.startsWith('http') 
        ? article.metadata.blogListImage 
        : `${baseUrl}${article.metadata.blogListImage}`;
      
      return `  <url>
    <loc>${baseUrl}/blog/${slug}</loc>
    <image:image>
      <image:loc>${imageUrl}</image:loc>
      <image:title>${article.structuredData.headline}</image:title>
      <image:caption>${article.metadata.description}</image:caption>
    </image:image>
  </url>`;
    });

  const xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">
${imageEntries.join('\n')}
</urlset>`;

  return xml;
}

// Generate sitemap index
function generateSitemapIndex() {
  const xml = `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <sitemap>
    <loc>${baseUrl}/sitemap.xml</loc>
    <lastmod>2024-12-20</lastmod>
  </sitemap>
  <sitemap>
    <loc>${baseUrl}/sitemap-images.xml</loc>
    <lastmod>2024-12-20</lastmod>
  </sitemap>
</sitemapindex>`;

  return xml;
}

// Write files
const publicDir = path.join(process.cwd(), 'public');

// Main sitemap
fs.writeFileSync(path.join(publicDir, 'sitemap.xml'), generateSitemap());
console.log('✅ Generated sitemap.xml');

// Image sitemap
fs.writeFileSync(path.join(publicDir, 'sitemap-images.xml'), generateImageSitemap());
console.log('✅ Generated sitemap-images.xml');

// Sitemap index
fs.writeFileSync(path.join(publicDir, 'sitemap-index.xml'), generateSitemapIndex());
console.log('✅ Generated sitemap-index.xml');

console.log(`📊 Total pages: ${allPages.length}`);
console.log(`📊 Article pages: ${articlePages.length}`);
console.log(`📊 Static pages: ${staticPages.length}`);
