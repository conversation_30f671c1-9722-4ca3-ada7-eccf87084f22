import { readFileSync, writeFileSync } from "node:fs";
import { join } from "node:path";
import { parseString, Builder } from "xml2js";
import { fileURLToPath } from "node:url";
import { dirname } from "node:path";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const generateRecentDates = (numUrls: number) => {
  const dates = [];
  const today = new Date();

  for (let i = 0; i < numUrls; i++) {
    const date = new Date(today);
    date.setDate(today.getDate() - (i % 4));
    dates.push(date.toISOString().split("T")[0]);
  }
  return dates;
};

const updateSitemapDates = async () => {
  const sitemapPath = join(process.cwd(), "public", "sitemap.xml");
  const xml = readFileSync(sitemapPath, "utf8");

  parseString(xml, (err, result) => {
    if (err) throw err;

    const dates = generateRecentDates(result.urlset.url.length);

    result.urlset.url.forEach((url: any, index: number) => {
      url.lastmod = [dates[index]];
    });

    const builder = new Builder();
    const updatedXml = builder.buildObject(result);

    writeFileSync(sitemapPath, updatedXml);
  });
};

updateSitemapDates();
