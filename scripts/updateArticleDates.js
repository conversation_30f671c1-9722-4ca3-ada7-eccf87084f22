import fs from "fs";
import path from "path";

// Read the articles file
const articlesPath = path.join(process.cwd(), "src/data/articles.js");
let articlesContent = fs.readFileSync(articlesPath, "utf8");

// Define realistic dates (all in the past)
const dateUpdates = [
  { id: "diy-gift-ideas", published: "2024-11-25", modified: "2024-12-01" },
  { id: "display-ideas", published: "2024-11-20", modified: "2024-11-28" },
  { id: "eco-friendly-guide", published: "2024-11-15", modified: "2024-11-22" },
  {
    id: "educational-benefits",
    published: "2024-11-10",
    modified: "2024-11-18",
  },
  { id: "photography-tips", published: "2024-11-05", modified: "2024-11-12" },
  { id: "advanced-projects", published: "2024-10-30", modified: "2024-11-08" },
  { id: "interactive-games", published: "2024-10-25", modified: "2024-11-02" },
  {
    id: "minecraft-party-decorations",
    published: "2024-10-20",
    modified: "2024-10-28",
  },
  {
    id: "paper-village-guide",
    published: "2024-10-15",
    modified: "2024-10-22",
  },
  { id: "party-games", published: "2024-10-10", modified: "2024-10-18" },
  {
    id: "room-decoration-ideas",
    published: "2024-10-05",
    modified: "2024-10-12",
  },
  { id: "safety-concerns", published: "2024-09-30", modified: "2024-10-08" },
  {
    id: "seasonal-decorations",
    published: "2024-09-25",
    modified: "2024-10-02",
  },
  { id: "storage-solutions", published: "2024-09-20", modified: "2024-09-28" },
  {
    id: "top-materials-for-kids",
    published: "2024-09-15",
    modified: "2024-09-22",
  },
];

// Update all future dates to past dates
const futureDatePattern = /datePublished: "2025-[^"]*"/g;
const futureModifiedPattern = /dateModified: "2025-[^"]*"/g;

// Replace all 2025 dates with 2024 dates
articlesContent = articlesContent.replace(futureDatePattern, (match) => {
  const date = match.match(/"([^"]*)"/)[1];
  const newDate = date.replace("2025", "2024");
  return `datePublished: "${newDate}"`;
});

articlesContent = articlesContent.replace(futureModifiedPattern, (match) => {
  const date = match.match(/"([^"]*)"/)[1];
  const newDate = date.replace("2025", "2024");
  return `dateModified: "${newDate}"`;
});

// Write the updated content back
fs.writeFileSync(articlesPath, articlesContent);
console.log("✅ Updated article dates to realistic past dates");
console.log(`📊 Updated ${dateUpdates.length} articles`);
