import PageLayout from "../components/PageLayout";

const CookiePolicy = () => {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    name: "Cookie Policy",
    description:
      "PaperCrafter's Cookie Policy - Understanding how we use cookies to improve your experience.",
    url: "https://papercrafter.netlify.app/cookie-policy",
  };

  return (
    <PageLayout
      title="Cookie Policy | PaperCrafter"
      description="Learn about how PaperCrafter uses cookies to improve your browsing experience. Understand the different types of cookies we use and how to manage them."
      keywords="cookie policy, website cookies, cookie management, essential cookies, analytics cookies"
      ogType="website"
      canonicalPath="/cookie-policy"
      structuredData={structuredData}
      loadingMessage="Loading Cookie Policy..."
    >
      <div className="container mx-auto px-4 py-8 bg-white/90 minecraft-overlay min-h-screen">
        <h1 className="text-3xl font-bold mb-6">Cookie Policy</h1>

        <section className="mb-8">
          <h2 className="text-2xl font-bold mb-4">What Are Cookies</h2>
          <p className="mb-4">
            Cookies are small text files that are placed on your device when you
            visit our website. They help us provide you with a better experience
            and are essential for some of our website&apos;s functionality.
          </p>
        </section>

        <section className="mb-8">
          <h2 className="text-2xl font-bold mb-4">Types of Cookies We Use</h2>
          <div className="mb-4">
            <h3 className="text-xl font-bold mb-2">Essential Cookies</h3>
            <p>
              Required for basic website functionality and cannot be disabled.
            </p>
          </div>
          <div className="mb-4">
            <h3 className="text-xl font-bold mb-2">Preference Cookies</h3>
            <p>
              Remember your settings and preferences for a better experience.
            </p>
          </div>
          <div className="mb-4">
            <h3 className="text-xl font-bold mb-2">Analytics Cookies</h3>
            <p>Help us understand how visitors interact with our website.</p>
          </div>
        </section>

        <section className="mb-8">
          <h2 className="text-2xl font-bold mb-4">Managing Cookies</h2>
          <p className="mb-4">
            You can control and/or delete cookies as you wish. You can delete
            all cookies that are already on your computer and you can set most
            browsers to prevent them from being placed.
          </p>
        </section>

        <section className="mb-8">
          <h2 className="text-2xl font-bold mb-4">Contact Us</h2>
          <p>
            If you have any questions about our use of cookies, please contact
            us at: <EMAIL>
          </p>
        </section>
      </div>
    </PageLayout>
  );
};

export default CookiePolicy;
