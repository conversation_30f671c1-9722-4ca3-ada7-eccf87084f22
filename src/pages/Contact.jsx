import React, { useState } from "react";
import PageLayout from "../components/PageLayout";

function Contact() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    message: "",
  });
  const [errors, setErrors] = useState({});
  const [submitStatus, setSubmitStatus] = useState("");

  const validateForm = () => {
    const newErrors = {};
    if (!formData.name.trim()) {
      newErrors.name = "Name is required";
    }
    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }
    if (!formData.message.trim()) {
      newErrors.message = "Message is required";
    }
    return newErrors;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const newErrors = validateForm();

    if (Object.keys(newErrors).length === 0) {
      try {
        const form = e.target;
        const data = new FormData(form);
        data.append("form-name", "contact");

        const response = await fetch("/", {
          method: "POST",
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
          body: new URLSearchParams(data).toString(),
        });

        if (response.ok) {
          setSubmitStatus("success");
          setFormData({ name: "", email: "", message: "" });
        } else {
          throw new Error("Form submission failed");
        }
      } catch (error) {
        console.error("Form submission error:", error);
        setSubmitStatus("error");
      }
    } else {
      setErrors(newErrors);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "ContactPage",
    mainEntityOfPage: {
      "@type": "WebPage",
      "@id": "https://papercrafter.netlify.app/contact",
    },
    name: "Contact PaperCrafter",
    description:
      "Get in touch with the PaperCrafter team for support, feedback, or questions about Minecraft papercraft.",
    url: "https://papercrafter.netlify.app/contact",
    publisher: {
      "@type": "Organization",
      name: "PaperCrafter",
      logo: {
        "@type": "ImageObject",
        url: "https://papercrafter.netlify.app/images/logo.png",
      },
    },
  };

  return (
    <PageLayout
      title="Contact Us | PaperCrafter - Minecraft Papercraft Support"
      description="Get in touch with the PaperCrafter team. We'd love to hear your feedback, answer questions, or help with your Minecraft papercraft journey."
      keywords="contact papercrafter, minecraft papercraft help, papercraft support, contact us, feedback, minecraft crafts support"
      ogType="website"
      canonicalPath="/contact"
      structuredData={structuredData}
      loadingMessage="Loading Contact Form...">
      <div className="container mx-auto p-4 lg:py-8 lg:px-8 w-full">
        <h1 className="minecraft-title text-4xl mb-8">Contact Us</h1>

        <div className="grid md:grid-cols-2 gap-8">
          {/* Contact Information Section */}
          <section className="bg-white p-6 rounded-lg shadow-lg">
            <h2 className="minecraft-subtitle text-2xl mb-4">Get in Touch</h2>
            <div className="space-y-4">
              <div className="benefit-card">
                <h3 className="font-bold text-xl mb-2">📧 Email Support</h3>
                <p className="text-xs md:text-base lg:text-xl">
                  Have questions or suggestions? Email me directly at{" "}
                  <a
                    href="mailto:<EMAIL>"
                    className="text-blue-600 hover:text-blue-800">
                    <EMAIL>
                  </a>
                </p>
              </div>

              <div className="benefit-card">
                <h3 className="font-bold text-xl mb-2">⏱️ Response Time</h3>
                <p className="text-xs md:text-base lg:text-xl">
                  I aim to respond to all inquiries within 24-48 hours during
                  business days. Your feedback helps me improve PaperCrafter for
                  everyone!
                </p>
              </div>

              <div className="benefit-card">
                <h3 className="font-bold text-xl mb-2">💡 Quick Tips</h3>
                <p className="text-xs md:text-base lg:text-xl">
                  Before contacting me, check out our How It Works page for
                  answers to common questions about creating Minecraft
                  papercraft.
                </p>
              </div>
            </div>
          </section>

          {/* Contact Form Section */}
          <section className="bg-white p-6 rounded-lg shadow-lg">
            <h2 className="minecraft-subtitle text-2xl mb-4">Send a Message</h2>

            {submitStatus === "success" && (
              <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                Message sent successfully! I'll get back to you soon.
              </div>
            )}

            {submitStatus === "error" && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                There was an error sending your message. Please try again.
              </div>
            )}

            <form
              name="contact"
              method="POST"
              netlify="true"
              data-netlify="true"
              onSubmit={handleSubmit}
              className="space-y-4">
              <input
                type="hidden"
                name="form-name"
                value="contact"
              />

              <div>
                <label
                  htmlFor="name"
                  className="block minecraft-text mb-2">
                  Name
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  className={`w-full p-2 border rounded ${
                    errors.name ? "border-red-500" : "border-gray-300"
                  }`}
                />
                {errors.name && (
                  <p className="text-red-500 text-sm mt-1">{errors.name}</p>
                )}
              </div>

              <div>
                <label
                  htmlFor="email"
                  className="block minecraft-text mb-2">
                  Email
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  className={`w-full p-2 border rounded ${
                    errors.email ? "border-red-500" : "border-gray-300"
                  }`}
                />
                {errors.email && (
                  <p className="text-red-500 text-sm mt-1">{errors.email}</p>
                )}
              </div>

              <div>
                <label
                  htmlFor="message"
                  className="block minecraft-text mb-2">
                  Message
                </label>
                <textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleChange}
                  rows="4"
                  className={`w-full p-2 border rounded ${
                    errors.message ? "border-red-500" : "border-gray-300"
                  }`}
                />
                {errors.message && (
                  <p className="text-red-500 text-sm mt-1">{errors.message}</p>
                )}
              </div>

              <button
                type="submit"
                className="bg-green-500 text-white px-6 py-2 rounded-lg hover:bg-green-600 transition-colors w-full">
                Send Message
              </button>
            </form>
          </section>
        </div>
      </div>
    </PageLayout>
  );
}

export default Contact;
