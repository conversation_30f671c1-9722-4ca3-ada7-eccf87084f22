import React from "react";
import { Helmet } from "react-helmet-async";
import { Link } from "react-router-dom";
import PageLayout from "../components/PageLayout";

const HowItWorks = () => {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "HowTo",
    "name": "How to Create Minecraft Papercraft",
    "description": "Step-by-step guide to creating Minecraft papercraft models using PaperCrafter",
    "image": "https://papercrafter.netlify.app/images/social-preview.png",
    "step": [
      {
        "@type": "HowToStep",
        "name": "Choose Your Model",
        "text": "Select from our collection of Minecraft-inspired papercraft templates"
      },
      {
        "@type": "HowToStep",
        "name": "Print Your Template",
        "text": "Download and print your chosen template on regular paper"
      },
      {
        "@type": "HowToStep",
        "name": "Cut and Fold",
        "text": "Follow the cutting lines and fold along the indicated edges"
      },
      {
        "@type": "HowToStep",
        "name": "Assemble",
        "text": "Use glue to connect the tabs and create your 3D model"
      }
    ]
  };

  return (
    <PageLayout
      title="How to Create Minecraft Papercraft | Step-by-Step Guide | PaperCrafter"
      description="Learn how to create amazing Minecraft papercraft models step by step. Easy-to-follow instructions for printing, cutting, and assembling your favorite Minecraft blocks and characters."
      keywords="minecraft papercraft tutorial, how to make minecraft papercraft, minecraft paper models, papercraft instructions, minecraft crafts, paper minecraft blocks"
      ogType="article"
      canonicalPath="/how-it-works"
      structuredData={structuredData}
      loadingMessage="Loading Tutorial..."
    >
      <div className="container mx-auto px-4 py-8 w-full lg:w-3/4 bg-slate-100">
        <h1 className="minecraft-title text-4xl mb-8">How It Works</h1>

        <div className="flex flex-col md:flex-row gap-8">
          <div className="space-y-4" id="step1">
            <h2 className="minecraft-subtitle text-2xl mb-4">
              Step 1: Choose Your Block
            </h2>
            <p className="minecraft-text">
              Browse our extensive collection of Minecraft blocks and select your favorite! Whether you're looking to create
              a grass block, diamond ore, or any other iconic Minecraft block, we've got you covered. You can customize
              the size to fit your needs - perfect for both small decorations and larger displays!
            </p>
            <img
              src="/images/screenshots/how-to-pick-textures.png"
              alt="Selecting a Minecraft block in PaperCrafter"
              className="rounded-lg shadow-lg"
            />
          </div>

          <div className="space-y-4" id="step2">
            <h2 className="minecraft-subtitle text-2xl mb-4">
              Step 2: Print Your Template
            </h2>
            <p className="minecraft-text">
              Once you've selected and customized your block, simply print the template on regular paper. Our templates
              are optimized for standard printer paper and include clear cutting lines and folding guides. Make sure your
              printer settings are set to 100% scale for the perfect size!
            </p>
          </div>

          <div className="space-y-4" id="step3">
            <h2 className="minecraft-subtitle text-2xl mb-4">
              Step 3: Cut, Fold, and Create!
            </h2>
            <p className="minecraft-text">
              Follow the cutting lines to carefully cut out your template. Then, fold along the indicated lines to create
              the 3D structure. Apply glue to the tabs and assemble your block. In just minutes, you'll have your very own
              3D Minecraft block ready for display!
            </p>
            <div className="bg-yellow-100 p-4 rounded-lg">
              <h3 className="font-bold mb-2">Pro Tips:</h3>
              <ul className="list-disc list-inside">
                <li>Use a ruler for straight cuts</li>
                <li>Score fold lines gently for cleaner folds</li>
                <li>Apply glue sparingly to avoid mess</li>
                <li>Let the glue dry completely before handling</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="mt-8 p-4 bg-green-100 rounded-lg">
          <h2 className="minecraft-subtitle text-2xl mb-4">Ready to Start Crafting?</h2>
          <p className="minecraft-text mb-4">
            Creating Minecraft papercraft has never been easier! With PaperCrafter, you can bring your favorite Minecraft
            blocks into the real world. Perfect for room decorations, school projects, or just for fun!
          </p>
          <button className="bg-green-500 text-white px-6 py-2 rounded-lg hover:bg-green-600 transition-colors">
            Create Your First Block
          </button>
        </div>
      </div>
    </PageLayout>
  );
};

export default HowItWorks;
