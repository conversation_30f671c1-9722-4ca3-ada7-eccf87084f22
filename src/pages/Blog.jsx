import { Link } from "react-router-dom";
import PageLayout from "../components/PageLayout";
import { articles } from "../data/articles";
import { v4 as uuidv4 } from "uuid";
import { useState } from "react";
import BlogListImage from "../components/BlogListImage";

const BlogCard = ({ id, article }) => {
  const { structuredData, metadata } = article;

  return (
    <>
      <Link
        to={`/blog/${id}`}
        className="block bg-white rounded-lg shadow-lg overflow-hidden transition-transform hover:scale-105 duration-300"
      >
        <div className="p-6">
          <div className="mb-2">
            <BlogListImage
              blogListImage={metadata.blogListImage}
              title={metadata.title}
            />
            <span className="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
              {metadata.category}
            </span>
          </div>
          <h2 className="minecraft-subtitle text-xl mb-2">
            {structuredData.headline}
          </h2>
          <p className="text-gray-600 mb-4">{metadata.description}</p>
          <div className="flex items-center text-sm text-gray-500">
            <span>{metadata.datePublished}</span>
          </div>
        </div>
      </Link>
    </>
  );
};

const Blog = () => {
  const [selectedCategory, setSelectedCategory] = useState("All");
  const categories = [
    "All",
    ...new Set(
      Object.values(articles).map((article) => article.metadata.category)
    ),
  ];

  const filteredArticles = Object.entries(articles).filter(
    ([_, article]) =>
      selectedCategory === "All" ||
      article.metadata.category === selectedCategory
  );

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Blog",
    name: "Minecraft Papercraft Blog",
    description:
      "Articles and guides about Minecraft papercraft projects and activities",
    url: "https://papercrafter.netlify.app/blog",
  };

  return (
    <PageLayout
      title="Minecraft Papercraft Blog | Tips, Tutorials & Ideas | PaperCrafter"
      description="Explore our collection of Minecraft papercraft articles, tutorials, and creative ideas. Learn new techniques, get inspired, and start crafting!"
      keywords="minecraft papercraft blog, minecraft craft tutorials, paper minecraft ideas, minecraft DIY projects, minecraft craft tips"
      ogType="website"
      canonicalPath="/blog"
      structuredData={structuredData}
      loadingMessage="Loading Blog..."
    >
      <div className="container mx-auto px-4 py-8">
        <h1 className="minecraft-title text-4xl mb-8 text-center">
          Minecraft Paper Craft Blog
        </h1>

        <div className="flex flex-wrap gap-2 justify-center mb-8">
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`px-4 py-2 rounded-full ${
                selectedCategory === category
                  ? "bg-green-600 text-white"
                  : "bg-gray-200 text-gray-700 hover:bg-gray-300"
              } transition-colors duration-200`}
            >
              {category}
            </button>
          ))}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredArticles.map(([id, article]) => (
            <BlogCard key={uuidv4()} id={id} article={article} />
          ))}
        </div>
      </div>
    </PageLayout>
  );
};

export default Blog;
