import PageLayout from "../components/PageLayout";

const PrivacyPolicy = () => {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    name: "Privacy Policy",
    description:
      "PaperCrafter's Privacy Policy - Learn how we collect, use, and protect your personal information.",
    url: "https://papercrafter.netlify.app/privacy-policy",
  };
  return (
    <>
      <PageLayout
        title="Privacy Policy | PaperCrafter"
        description="Learn how PaperCrafter collects, uses, and protects your personal information. Our privacy policy explains your rights and our data practices."
        keywords="privacy policy, data protection, GDPR rights, personal information, data collection"
        ogType="website"
        canonicalPath="/privacy-policy"
        structuredData={structuredData}
        loadingMessage="Loading Privacy Policy..."
      >
        <div className="container mx-auto px-4 py-8 bg-white/90 minecraft-overlay min-h-screen">
          <h1 className="text-3xl font-bold mb-6">Privacy Policy</h1>

          <section className="mb-8">
            <h2 className="text-2xl font-bold mb-4">1. Introduction</h2>
            <p className="mb-4">
              Welcome to PaperCrafter. This Privacy Policy explains how we
              collect, use, and protect your personal information.
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold mb-4">
              2. Information We Collect
            </h2>
            <ul className="list-disc pl-6 mb-4">
              <li>Technical information (IP address, browser type)</li>
              <li>Cookie data</li>
              <li>User preferences and settings</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold mb-4">
              3. How We Use Your Information
            </h2>
            <ul className="list-disc pl-6 mb-4">
              <li>To provide and improve our services</li>
              <li>To personalize your experience</li>
              <li>To analyze usage patterns</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold mb-4">4. Your Rights</h2>
            <p className="mb-4">Under GDPR, you have the right to:</p>
            <ul className="list-disc pl-6 mb-4">
              <li>Access your personal data</li>
              <li>Rectify inaccurate data</li>
              <li>Request data erasure</li>
              <li>Object to data processing</li>
              <li>Data portability</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold mb-4">5. Contact Us</h2>
            <p>
              For any privacy-related questions, please contact us at:
              <EMAIL>
            </p>
          </section>
        </div>
      </PageLayout>
    </>
  );
};

export default PrivacyPolicy;
