import { Link } from "react-router-dom";
import ArticleTemplate, {
  ArticleSection,
  BenefitCard,
} from "../components/ArticleTemplate";

const Benefits = () => {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Article",
    mainEntityOfPage: {
      "@type": "WebPage",
      "@id": "https://papercrafter.netlify.app/benefits",
    },
    headline: "Benefits of Minecraft Papercraft for Kids",
    description:
      "Discover how Minecraft papercraft enhances creativity, motor skills, and STEM learning",
    image: "https://papercrafter.netlify.app/images/social-preview.png",
    datePublished: "2024-01-20",
    dateModified: "2024-01-20",
    author: {
      "@type": "Person",
      name: "<PERSON><PERSON>",
    },
    publisher: {
      "@type": "Organization",
      name: "<PERSON><PERSON>raft<PERSON>",
      logo: {
        "@type": "ImageObject",
        url: "https://papercrafter.netlify.app/images/logo.png",
      },
    },
  };

  return (
    <ArticleTemplate
      title="Benefits of Minecraft Papercraft for Kids"
      description="Discover the amazing benefits of Minecraft papercraft! Boost creativity, improve fine motor skills, enhance spatial awareness, and balance screen time. Perfect educational activity for kids who love Minecraft."
      keywords="minecraft papercraft benefits, educational minecraft activities, kids paper crafting, minecraft learning, fine motor skills, spatial awareness, screen time balance, creative activities, STEM learning, minecraft education"
      ogType="article"
      canonicalPath="/benefits"
      structuredData={structuredData}
      loadingMessage="Loading Benefits..."
    >
      <ArticleSection title="How Papercraft Enhances Learning?">
        <BenefitCard title="🧮 STEM Learning">
          <p className="text-xs md:text-base lg:text-xl">
            Papercraft naturally introduces mathematical concepts like geometry,
            measurement, and spatial reasoning. Children learn about 3D shapes,
            angles, and proportions while creating their favorite Minecraft
            blocks.
          </p>
        </BenefitCard>

        <BenefitCard title="🎨 Fine Motor Skills">
          <p className="text-xs md:text-base lg:text-xl">
            Cutting, folding, and assembling paper blocks helps develop crucial
            fine motor skills and hand-eye coordination. These skills are
            essential for writing, drawing, and other detailed tasks.
          </p>
        </BenefitCard>

        <BenefitCard title="🧩 Problem Solving">
          <p className="text-xs md:text-base lg:text-xl">
            Following templates and figuring out how pieces fit together
            enhances problem-solving abilities and logical thinking. Kids learn
            to plan, sequence steps, and troubleshoot challenges.
          </p>
        </BenefitCard>

        <BenefitCard title="👀 Spatial Awareness">
          <p className="text-xs md:text-base lg:text-xl">
            Papercraft challenges spatial awareness and attention span. It helps
            children develop visual perception, spatial reasoning, and spatial
            memory.
          </p>
        </BenefitCard>
      </ArticleSection>

      <ArticleSection title="Creative & Social Benefits">
        <BenefitCard title="🎭 Creative Expression">
          <p className="text-xs md:text-base lg:text-xl">
            Transform digital designs into tangible creations! Kids can
            customize their blocks, create unique displays, and bring their
            Minecraft imagination into the real world.
          </p>
        </BenefitCard>

        <BenefitCard title="👥 Social Interaction">
          <p className="text-xs md:text-base lg:text-xl">
            Papercraft becomes a social activity when children craft together,
            share techniques, and collaborate on projects. It's a great way to
            build friendships and communication skills.
          </p>
        </BenefitCard>

        <BenefitCard title="📱 Screen Time Balance">
          <p className="text-xs md:text-base lg:text-xl">
            Bridge the gap between digital and physical play! Papercraft offers
            a healthy way to extend Minecraft enjoyment beyond the screen while
            developing valuable hands-on skills.
          </p>
        </BenefitCard>
      </ArticleSection>

      <section className="mt-8 bg-green-50 p-6 rounded-lg">
        <h2 className="minecraft-subtitle text-2xl mb-4">Tips for Parents</h2>
        <div className="grid md:grid-cols-2 gap-4">
          <div className="tip-card">
            <h3 className="font-bold mb-2">🎯 Set Crafting Goals</h3>
            <p className="text-xs md:text-base lg:text-xl">
              Start with simple blocks and gradually move to more complex
              designs. This builds confidence and maintains interest.
            </p>
          </div>
          <div className="tip-card">
            <h3 className="font-bold mb-2">🤝 Make It Interactive</h3>
            <p className="text-xs md:text-base lg:text-xl">
              Craft alongside your child or invite friends over for crafting
              sessions. This makes learning more engaging and fun!
            </p>
          </div>
        </div>
      </section>

      <div className="mt-8 text-center">
        <Link
          to="/"
          className="inline-block bg-green-500 text-white px-8 py-3 rounded-lg hover:bg-green-600 transition-colors"
        >
          Start Crafting Today!
        </Link>
      </div>
    </ArticleTemplate>
  );
};

export default Benefits;
