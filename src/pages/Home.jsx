import { useState, useEffect } from "react";
import { blocks } from "../utils/blocks";
import usePrintMedia from "../hooks/usePrintMedia";
import CustomIcon from "../components/CustomIcon";
import Accordion from "../components/Accordion";
import UnfoldedCube from "../components/UnfoldedCube";
import Sidebar from "../components/Sidebar";

function Home() {
  const [selectedTexture, setSelectedTexture] = useState(null);
  const [cube1Faces, setCube1Faces] = useState({
    front: null,
    back: null,
    left: null,
    right: null,
    top: null,
    bottom: null,
  });
  const [cube2Faces, setCube2Faces] = useState({
    front: null,
    back: null,
    left: null,
    right: null,
    top: null,
    bottom: null,
  });
  const [size, setSize] = useState(90);
  const [searchQuery, setSearchQuery] = useState("");
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const isPrinting = usePrintMedia();

  const handleTextureSelect = (texture) => {
    setSelectedTexture(texture);
  };

  const handleFaceClick = (face, cubeIndex) => {
    if (selectedTexture) {
      const setCubeFaces = cubeIndex === 1 ? setCube1Faces : setCube2Faces;
      setCubeFaces((prev) => ({
        ...prev,
        [face]: selectedTexture,
      }));
    }
  };

  const handleClearFace = (face, cubeIndex) => {
    const setCubeFaces = cubeIndex === 1 ? setCube1Faces : setCube2Faces;
    setCubeFaces((prev) => ({
      ...prev,
      [face]: null,
    }));
  };

  const handleClearCube = (cubeIndex) => {
    const setCubeFaces = cubeIndex === 1 ? setCube1Faces : setCube2Faces;
    setCubeFaces({
      front: null,
      back: null,
      left: null,
      right: null,
      top: null,
      bottom: null,
    });
  };

  const handleResize = () => {
    let baseSize;

    if (window.innerWidth < 370) {
      baseSize = 50;
    } else if (window.innerWidth < 420) {
      baseSize = 65;
    } else if (window.innerWidth < 490) {
      baseSize = 75;
    } else if (window.innerWidth < 595) {
      baseSize = 90;
    } else if (window.innerWidth < 768) {
      baseSize = 120;
    } else if (window.innerWidth < 924) {
      baseSize = 70;
    } else if (window.innerWidth < 1024) {
      baseSize = 90;
    } else if (window.innerWidth < 1170) {
      baseSize = 100;
    } else {
      baseSize = 120;
    }

    // Further adjust based on container constraints
    const containerWidth =
      window.innerWidth * (window.innerWidth < 768 ? 0.9 : 0.66); // Full width on mobile
    const containerHeight = window.innerHeight - 200;

    const maxWidth = containerWidth / 2.5;
    const maxHeight = containerHeight / 1.5;

    const newSize = Math.min(baseSize, maxWidth, maxHeight);

    setSize(Math.floor(newSize));
  };

  const handlePrint = () => {
    if (typeof window !== "undefined" && window.matchMedia) {
      const mediaQueryList = window.matchMedia("print");

      if (mediaQueryList.matches || "onbeforeprint" in window) {
        setSize(120);
        setTimeout(() => {
          window.print();
        }, 200);
      } else {
        alert("To print: Use your browser menu or press Ctrl+P (CMD+P on Mac)");
      }
    } else {
      setSize(120);
      setTimeout(() => {
        window.print();
      }, 200);
    }
  };

  useEffect(() => {
    if (!isPrinting) {
      handleResize();
    }
  }, [isPrinting]);

  useEffect(() => {
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return (
    <>
      <div className="max-w-full md:max-w-7xl mx-auto md:flex md:gap-8 print:max-w-none print:w-full">
        <Accordion title="How It Works">
          <div className="flex flex-col gap-8">
            <div className="space-y-4 flex-1">
              <h2 className="minecraft-subtitle text-2xl mb-4">
                Step 1: Choose Your Block
              </h2>
              <p className="minecraft-text">
                Select any Minecraft block from our collection. Just click on
                the block to select it and then click on the face to add it to
                your template.
              </p>
            </div>

            <div className="space-y-4 flex-1">
              <h2 className="minecraft-subtitle text-2xl mb-4">
                Step 2: Print & Craft
              </h2>
              <p className="minecraft-text">
                Print your template on regular paper, cut along the lines, fold
                the tabs, and glue it together. It&apos;s that simple!
              </p>
            </div>
          </div>
        </Accordion>
      </div>

      <div className="max-w-full md:max-w-7xl mx-auto md:flex md:gap-8 print:max-w-none print:w-full">
        {/* Main content */}
        <div className="minecraft-panel w-full md:w-2/3 overflow-x-hidden print:w-full print:overflow-visible print:minecraft-panel-flat">
          <h2 className="minecraft-title text-xl mb-4 print:hidden">
            Template Preview
          </h2>
          <div className="print-area flex flex-col gap-8 min-w-fit print:min-w-0 print:w-full">
            <div className="printMarkup print:visible flex flex-col items-left print:items-left">
              <h1 className="hidden print:block minecraft-title-print text-4xl">
                Papercrafter
              </h1>
              <div className="cube-container mb-4 print:mb-0">
                <div className="print:hidden mb-2 flex justify-between items-center">
                  <button
                    className="minecraft-btn text-sm py-1"
                    onClick={() => handleClearCube(1)}
                  >
                    Clear Cube
                  </button>
                </div>
                <UnfoldedCube
                  faces={cube1Faces}
                  onFaceClick={(face) => handleFaceClick(face, 1)}
                  onClearFace={(face) => handleClearFace(face, 1)}
                  size={size}
                  containerSize={isPrinting ? size * 4 - 40 : size * 4}
                />
              </div>
              <div className="cube-container ">
                <div className="print:hidden mb-2 flex justify-between items-center">
                  <button
                    className="minecraft-btn text-sm py-1"
                    onClick={() => handleClearCube(2)}
                  >
                    Clear Cube
                  </button>
                </div>
                <UnfoldedCube
                  faces={cube2Faces}
                  onFaceClick={(face) => handleFaceClick(face, 2)}
                  onClearFace={(face) => handleClearFace(face, 2)}
                  size={size}
                  containerSize={isPrinting ? size * 4 - 40 : size * 4}
                  rotation={180}
                />
              </div>
              <h4 className="hidden print:block minecraft-footer-print text-sm">
                made with <CustomIcon image="/images/pickaxe.png" size={16} />{" "}
                by Manu
                <br />
                <a href="mailto:<EMAIL>">
                  <EMAIL>
                </a>
              </h4>
            </div>
          </div>
          {/* Print Button */}
          <button
            className="minecraft-btn mt-4 print:hidden"
            onClick={handlePrint}
          >
            Print Templates
          </button>
        </div>

        {/* Sidebar */}
        <Sidebar
          blocks={blocks}
          selectedTexture={selectedTexture}
          handleTextureSelect={handleTextureSelect}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          isOpen={isSidebarOpen}
          setIsOpen={setIsSidebarOpen}
        />
      </div>
    </>
  );
}

export default Home;
