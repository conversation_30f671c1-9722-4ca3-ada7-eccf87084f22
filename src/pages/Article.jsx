import { useParams, Navigate } from "react-router-dom";
import ArticleTemplate, {
  ArticleSection,
  BenefitCard,
} from "../components/ArticleTemplate";
import FinalTips from "../components/FinalTips";
import { articles } from "../data/articles";

const Article = () => {
  const { articleId } = useParams();
  const article = articles[articleId];

  if (!article) {
    return (
      <Navigate
        to="/blog"
        replace
      />
    );
  }

  const { structuredData, metadata, content } = article;

  return (
    <ArticleTemplate
      title={structuredData.headline}
      description={metadata.description}
      keywords={metadata.keywords}
      ogType={metadata.ogType}
      canonicalPath={metadata.canonicalPath}
      structuredData={structuredData}
      datePublished={metadata.datePublished}
      dateModified={metadata.dateModified}
      category={metadata.category}
      wordCount={metadata.wordCount}
      readingTime={metadata.readingTime}
      author={structuredData.author?.name}
    >
      {content.sections.map((section, index) => (
        <ArticleSection
          key={index}
          title={section.title}
        >
          {section.benefitCards.map((card, cardIndex) => (
            <BenefitCard
              key={cardIndex}
              title={card.title}
              emoji={card.emoji}
            >
              <p>{card.text}</p>
            </BenefitCard>
          ))}
        </ArticleSection>
      ))}
      <FinalTips
        title={content.finalTips.title}
        text={content.finalTips.text}
      />
    </ArticleTemplate>
  );
};

export default Article;
