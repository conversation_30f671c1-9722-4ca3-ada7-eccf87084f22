import { Link } from "react-router-dom";
import PageLayout from "../components/PageLayout";

const WhyMinecraft = () => {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Article",
    mainEntityOfPage: {
      "@type": "WebPage",
      "@id": "https://papercrafter.netlify.app/why-minecraft",
    },
    headline: "Why Choose Minecraft for Educational Crafting?",
    description:
      "Discover why Minecraft is the perfect platform for educational paper crafting. Learn about its impact on creativity, learning, and child development.",
    image: "https://papercrafter.netlify.app/images/social-preview.png",
    datePublished: "2024-01-20",
    dateModified: "2024-01-20",
    author: {
      "@type": "Person",
      name: "<PERSON><PERSON>",
    },
    publisher: {
      "@type": "Organization",
      name: "<PERSON><PERSON>rafter",
      logo: {
        "@type": "ImageObject",
        url: "https://papercrafter.netlify.app/images/logo.png",
      },
    },
  };

  return (
    <PageLayout
      title="Why Minecraft Papercraft? | Educational Gaming Meets Creative Crafting | PaperCrafter"
      description="Discover why Minecraft is the perfect platform for educational paper crafting. Learn about its impact on creativity, learning, and child development."
      keywords="minecraft education, minecraft learning benefits, minecraft creativity, educational gaming, minecraft for kids, minecraft paper crafts, creative learning"
      ogType="article"
      canonicalPath="/why-minecraft"
      structuredData={structuredData}
      loadingMessage="Loading Why Minecraft...">
      <div className="container mx-auto p-4 lg:px-8 lg:py-8 w-full">
        <h1 className="minecraft-title text-4xl mb-8">Why Minecraft?</h1>

        <div className="grid md:grid-cols-2 gap-8">
          <section className="bg-white p-6 rounded-lg shadow-lg">
            <h2 className="minecraft-subtitle text-2xl mb-4">
              The Minecraft Phenomenon
            </h2>
            <div className="space-y-4">
              <div className="benefit-card">
                <h3 className="font-bold text-xl mb-2">🌟 Global Impact</h3>
                <p className="text-xs md:text-base lg:text-xl">
                  Since its release in 2011, Minecraft has become more than just
                  a game - it's a creative platform that inspires millions
                  worldwide. With over 238 million copies sold, it stands as the
                  best-selling video game of all time, demonstrating its
                  universal appeal and lasting impact.
                </p>
              </div>

              <div className="benefit-card">
                <h3 className="font-bold text-xl mb-2">🎮 Creative Freedom</h3>
                <p className="text-xs md:text-base lg:text-xl">
                  What makes Minecraft special is its unique blend of
                  creativity, exploration, and problem-solving. Players can
                  build anything they imagine in a world made entirely of
                  blocks, fostering creativity and spatial thinking skills.
                </p>
              </div>

              <div className="benefit-card">
                <h3 className="font-bold text-xl mb-2">🎓 Educational Tool</h3>
                <p className="text-xs md:text-base lg:text-xl">
                  The game's influence extends beyond entertainment, being
                  widely used in educational settings to teach everything from
                  basic math to computer programming. Its simple yet versatile
                  block-based design has revolutionized how we think about
                  digital creativity and learning.
                </p>
              </div>
            </div>
          </section>

          <section className="bg-white p-6 rounded-lg shadow-lg">
            <h2 className="minecraft-subtitle text-2xl mb-4">
              Educational Benefits
            </h2>
            <div className="space-y-4">
              <div className="benefit-card">
                <h3 className="font-bold text-xl mb-2">
                  🧠 Cognitive Development
                </h3>
                <p className="text-xs md:text-base lg:text-xl">
                  Minecraft's block-based world creates an ideal environment for
                  developing crucial cognitive skills. Children exercise their
                  minds through decision-making, problem-solving, and critical
                  thinking about resource management.
                </p>
              </div>

              <div className="benefit-card">
                <h3 className="font-bold text-xl mb-2">📐 Spatial Reasoning</h3>
                <p className="text-xs md:text-base lg:text-xl">
                  Building complex structures helps develop spatial reasoning
                  abilities crucial for mathematics, engineering, and
                  architecture. The game's 3D environment enhances understanding
                  of geometry and spatial relationships.
                </p>
              </div>

              <div className="benefit-card">
                <h3 className="font-bold text-xl mb-2">💡 STEM Learning</h3>
                <p className="text-xs md:text-base lg:text-xl">
                  The game's redstone mechanics introduce basic programming
                  concepts like logic gates and circuits, laying a foundation
                  for future technology learning. This natural integration of
                  STEM concepts makes learning engaging and fun.
                </p>
              </div>
            </div>
          </section>
        </div>

        <div className="mt-8 text-center">
          <Link
            to="/"
            className="inline-block bg-green-500 text-white px-8 py-3 rounded-lg hover:bg-green-600 transition-colors">
            Start Crafting Today!
          </Link>
        </div>
      </div>
    </PageLayout>
  );
};

export default WhyMinecraft;
