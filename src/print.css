@media print {
  @page {
    size: A4 portrait;
    margin: 0;
  }
  #root {
    width: fit-content !important;
  }
  /* Hide non-printable elements */
  header,
  .texture-panel,
  .print-button,
  .print\:hidden {
    display: none !important;
  }

  /* Reset background colors for printing */
  body {
    margin: 0;
    padding: 0;
    background: white !important;
  }

  .preview-panel {
    box-shadow: none !important;
    padding: 0 !important;
    margin: 0 !important;
    width: 100% !important;
  }

  /* Center content on the page */
  .preview-panel {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
  }

  .print-area {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
    width: 210mm; /* A4 width */
    height: 297mm; /* A4 height */
    padding: 10mm;
    margin: 0;
    box-sizing: border-box;
  }

  .cube-container {
    page-break-inside: avoid;
    break-inside: avoid;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 135mm; /* Roughly half of printable area */
  }

  .minecraft-panel {
    background: none !important;
    border: none !important;
    box-shadow: none !important;
    padding: 0 !important;
    margin: 0 !important;
    width: 100% !important;
  }

  /* Remove any margins or padding that might affect centering */
  .relative {
    margin: 0 !important;
    padding: 0 !important;
  }

  /* Hide borders in print */
  [style*="border"] {
    border: none !important;
  }

  /* Ensure each cube fits within its container */
  .cube-container > div {
    transform: scale(0.95);
    transform-origin: center center;
  }

  .printMarkup {
    border: 4px dashed black;
    width: 100%;
    height: 100%;
    align-items: center;
  }
  .minecraft-title-print {
    text-shadow: 2px 2px #c7c7c7;
    color: black;
    letter-spacing: 1px;
    position: relative;
    top: -24px;
    background-color: #fff;
    padding: 0 24px;
  }

  .minecraft-footer-print {
    text-shadow: 2px 2px #c7c7c7;
    color: black;
    letter-spacing: 1px;
    position: relative;
    bottom: -20px;
    background-color: #fff;
    padding: 0 24px;
    text-align: center;
  }
}
