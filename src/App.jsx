import { useState, useEffect } from "react";
import { BrowserRouter as Router, useLocation } from "react-router-dom";
import { Helmet<PERSON>rovider, Helmet } from "react-helmet-async";
import ReactGA from "react-ga4";
import Header from "./components/Header";
import Footer from "./components/Footer";
import Navigation from "./components/Navigation";
import CookieBanner from "./components/CookieBanner";

import "./minecraft-style.css";
import "./print.css";
import "./footer.css";
import "./navigation.css";
import AppRoutes from "./components/AppRoutes";

// Initialize GA4
ReactGA.initialize("G-C4NP74SX97");

// Create a component to track page views
const PageTracker = () => {
  const location = useLocation();

  useEffect(() => {
    ReactGA.send({ hitType: "pageview", page: location.pathname });
  }, [location]);

  return null;
};

function App() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isCookieModalOpen, setIsCookieModalOpen] = useState(false);
  useEffect(() => {
    // Check if user has already set cookie preferences
    const consent = localStorage.getItem("cookieConsent");
    if (!consent) {
      setIsCookieModalOpen(true);
    }

    // Listen for cookie settings button click
    const handleOpenCookieSettings = () => setIsCookieModalOpen(true);
    window.addEventListener("openCookieSettings", handleOpenCookieSettings);

    return () => {
      window.removeEventListener(
        "openCookieSettings",
        handleOpenCookieSettings
      );
    };
  }, []);

  return (
    <HelmetProvider>
      <Router>
        <PageTracker />
        <Helmet>
          <title>PaperCrafter - Minecraft Papercraft Template Creator</title>
          <meta
            name="description"
            content="Create and customize Minecraft papercraft templates with PaperCrafter. Design your own 3D paper models of Minecraft characters, buildings, and more!"
          />
          <meta property="og:title" content="PaperCrafter" />
          <meta
            property="og:description"
            content="Create and customize Minecraft papercraft templates with PaperCrafter."
          />
          <meta property="og:type" content="website" />
          <meta property="og:url" content="https://papercrafter.net" />
          <link rel="icon" href="/minecraft_icon.svg" type="image/svg+xml" />
        </Helmet>
        <Header />
        <Navigation isOpen={isMenuOpen} setIsOpen={setIsMenuOpen} />
        <AppRoutes />
        <CookieBanner
          isOpen={isCookieModalOpen}
          onClose={() => setIsCookieModalOpen(false)}
        />
        <Footer />
      </Router>
    </HelmetProvider>
  );
}

export default App;
