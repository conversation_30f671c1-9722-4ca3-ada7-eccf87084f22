import { useState, useEffect } from 'react';

const usePrintMedia = () => {
  const [isPrinting, setIsPrinting] = useState(false);

  useEffect(() => {
    const handlePrintStart = () => setIsPrinting(true);
    const handlePrintEnd = () => setIsPrinting(false);

    // Add event listeners for different print events
    window.addEventListener('beforeprint', handlePrintStart);
    window.addEventListener('afterprint', handlePrintEnd);

    // For Safari and mobile browsers
    if (window.matchMedia) {
      const mediaQueryList = window.matchMedia('print');
      const handleChange = (mql) => {
        setIsPrinting(mql.matches);
      };

      mediaQueryList.addListener(handleChange);

      return () => {
        window.removeEventListener('beforeprint', handlePrintStart);
        window.removeEventListener('afterprint', handlePrintEnd);
        mediaQueryList.removeListener(handleChange);
      };
    }

    return () => {
      window.removeEventListener('beforeprint', handlePrintStart);
      window.removeEventListener('afterprint', handlePrintEnd);
    };
  }, []);

  return isPrinting;
};

export default usePrintMedia;
