/* Minecraft-inspired button */
.minecraft-btn {
  @apply relative px-4 py-2 text-white font-bold cursor-pointer select-none;
  background-color: #757575;
  border: 2px solid #1D1D1D;
  box-shadow: inset -2px -4px #0004, inset 2px 2px #FFF7;
  image-rendering: pixelated;
  text-shadow: 2px 2px #000;
}

.minecraft-btn:hover {
  background-color: #8B8B8B;
}

.minecraft-btn:active {
  box-shadow: inset -2px -4px #0004;
}

/* Minecraft-inspired panel */
.minecraft-panel {
  @apply relative p-4;
  background-color: #C6C6C6;
  border: 2px solid #1D1D1D;
  box-shadow: inset 0 0 0 4px #8B8B8B, inset 0 0 0 6px #1D1D1D;
}

/* Minecraft-inspired title */
.minecraft-title {
  @apply font-bold;
  text-shadow: 2px 2px #000;
  color: #FFFFFF;
}

/* Minecraft-style scrollbar */
.minecraft-scrollbar::-webkit-scrollbar {
  width: 12px;
}

.minecraft-scrollbar::-webkit-scrollbar-track {
  background: #8B8B8B;
  border: 2px solid #1D1D1D;
}

.minecraft-scrollbar::-webkit-scrollbar-thumb {
  background: #C6C6C6;
  border: 2px solid #1D1D1D;
}

.minecraft-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #DBDBDB;
}

/* Minecraft-style input */
.minecraft-input {
  background-color: #8B8B8B;
  border: 2px solid #1D1D1D;
  color: white;
}

.minecraft-input::placeholder {
  color: #DBDBDB;
}

.minecraft-input:focus {
  outline: none;
  box-shadow: 0 0 0 2px #4A8F36;
}
