.masonry-grid {
  display: flex;
  flex-flow: row wrap;
  gap: 1.5rem;
  padding: 1rem;
}

.masonry-grid > * {
  flex: 1 1 300px;
  margin-bottom: 1.5rem;
  break-inside: avoid;
  display: flex;
}

@media (min-width: 640px) {
  .masonry-grid {
    column-gap: 2rem;
  }
  .masonry-grid > * {
    flex-basis: calc(50% - 1rem);
  }
}

@media (min-width: 1024px) {
  .masonry-grid > * {
    flex-basis: calc(33.333% - 1.333rem);
  }
}
