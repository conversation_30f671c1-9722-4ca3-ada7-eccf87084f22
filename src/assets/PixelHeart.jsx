import React from 'react';

const PixelHeart = ({ className = "", size = 16 }) => {
  return (
    <svg 
      width={size} 
      height={size} 
      viewBox="0 0 16 16" 
      className={className}
      style={{ display: 'inline-block', verticalAlign: 'middle' }}
    >
      <path
        fill="currentColor"
        d="M8 4.5L7 3.5L5 3.5L4 4.5L3 5.5L3 7.5L4 8.5L5 9.5L6 10.5L7 11.5L8 12.5L9 11.5L10 10.5L11 9.5L12 8.5L13 7.5L13 5.5L12 4.5L11 3.5L9 3.5L8 4.5Z"
      />
    </svg>
  );
};

export default PixelHeart;
