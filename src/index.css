@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: "Minecraft";
  src: url("/MinecraftRegular.otf") format("opentype");
  font-weight: normal;
  font-style: normal;
}

:root {
  font-family: "Minecraft", system-ui, -apple-system, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color: #1d1d1d;
  -webkit-font-smoothing: none;
  -moz-osx-font-smoothing: unset;
}

body {
  margin: 0;
  min-height: 100vh;
  min-width: 320px;
  background-color: rgb(73 76 73);
  background-image: url(/images/grass_block_top.png);
  background-size: 512px;
  background-blend-mode: multiply;
  image-rendering: pixelated;
}

.minecraft-overlay {
  position: relative;
}

.minecraft-overlay::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(92, 143, 86, 0.3);
  z-index: -1;
}

.minecraft-btn {
  @apply relative px-4 py-2 text-white font-bold cursor-pointer select-none;
  background-color: #757575;
  border: 2px solid #1d1d1d;
  box-shadow: inset -2px -4px #0004, inset 2px 2px #fff7;
  text-shadow: 2px 2px #000;
}

.minecraft-btn:hover {
  background-color: #8b8b8b;
}

.minecraft-btn:active {
  box-shadow: inset -2px -4px #0004;
}

.minecraft-panel {
  @apply relative p-4;
  background-color: #c6c6c6;
  border: 2px solid #1d1d1d;
  box-shadow: inset 0 0 0 4px #8b8b8b, inset 0 0 0 6px #1d1d1d;
}

.minecraft-panel::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 0;
}

.minecraft-panel > * {
  position: relative;
  z-index: 1;
}

.minecraft-title {
  @apply font-bold;
  text-shadow: 2px 2px #000;
  color: #ffffff;
  letter-spacing: 1px;
}

.minecraft-scrollbar::-webkit-scrollbar {
  width: 12px;
}

.minecraft-scrollbar::-webkit-scrollbar-track {
  background: #8b8b8b;
  border: 2px solid #1d1d1d;
}

.minecraft-scrollbar::-webkit-scrollbar-thumb {
  background: #c6c6c6;
  border: 2px solid #1d1d1d;
}

.minecraft-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #dbdbdb;
}

.minecraft-input {
  background-color: #8b8b8b;
  border: 2px solid #1d1d1d;
  color: white;
}

.minecraft-input::placeholder {
  color: #dbdbdb;
}

.minecraft-input:focus {
  outline: none;
  box-shadow: 0 0 0 2px #4a8f36;
}

.minecraft-bg {
  padding: 1rem;
  background-color: rgb(73, 76, 73);
  background-image: url("/images/grass_block_top.png");
  background-size: 512px;
  background-blend-mode: multiply;
  image-rendering: pixelated;
}

.minecraft-bg.print {
  padding: 0;
  background: white;
}

.minecraft-checkbox {
  appearance: none;
  width: 24px;
  height: 24px;
  border: 2px solid #1d1d1d;
  background-color: #757575;
  box-shadow: inset -2px -4px #0004, inset 2px 2px #fff7;
  cursor: pointer;
  position: relative;
}

.minecraft-checkbox:checked {
  background-color: #5cb85c;
}

.minecraft-checkbox:checked::after {
  content: "✔";
  position: absolute;
  color: white;
  font-size: 16px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.minecraft-checkbox:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}

a:hover {
  color: #535bf2;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slideDown {
  animation: slideDown 0.2s ease-out;
}

/* Cube Grid Styles */
.cube-container {
  transform-style: preserve-3d;
}

.cube {
  transform-style: preserve-3d;
  transition: transform 0.5s;
}

.cube:hover {
  transform: rotateY(180deg);
}

.cube-face {
  backface-visibility: hidden;
}

.cube-flap {
  pointer-events: none;
  transition: transform 0.3s;
}

.rotate-90 {
  transform: rotate(90deg);
}

.rotate-180 {
  transform: rotate(180deg);
}

.rotate-270 {
  transform: rotate(270deg);
}

.origin-top {
  transform-origin: top;
}

.origin-bottom {
  transform-origin: bottom;
}

.origin-left {
  transform-origin: left;
}

.origin-right {
  transform-origin: right;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
}
