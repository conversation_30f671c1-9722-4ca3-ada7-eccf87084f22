import { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import ReactGA from "react-ga4";

// Function to set actual browser cookies with secure attributes
const setCookie = (name, value, days) => {
  const date = new Date();
  date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
  const expires = `expires=${date.toUTCString()}`;
  const domain =
    window.location.hostname === "localhost"
      ? ""
      : "domain=.papercrafter.netlify.app;";
  document.cookie = `${name}=${value};${expires};path=/;${domain}SameSite=Strict;Secure`;
};

// Function to get cookie value with improved parsing
const getCookie = (name) => {
  const cookies = document.cookie.split(";").reduce((acc, cookie) => {
    const [key, value] = cookie.trim().split("=");
    acc[key] = value;
    return acc;
  }, {});
  return cookies[name] || null;
};

// Function to manage Google Analytics
const manageAnalytics = (enabled) => {
  if (enabled) {
    // Initialize GA4
    ReactGA.initialize("G-C4NP74SX97");
    // Enable analytics
    window["ga-disable-G-C4NP74SX97"] = false;
  } else {
    // Disable analytics
    window["ga-disable-G-C4NP74SX97"] = true;
    // Remove GA cookies
    setCookie("_ga", "", -1);
    setCookie("_ga_C4NP74SX97", "", -1);
    setCookie("_gat", "", -1);
    setCookie("_gid", "", -1);
  }
};

const CookieBanner = ({ isOpen, onClose }) => {
  const [cookiePreferences, setCookiePreferences] = useState({
    essential: true,
    analytics: false,
    preferences: false,
  });

  // Load saved preferences from cookies on mount
  useEffect(() => {
    const savedConsent = getCookie("cookieConsent");
    if (savedConsent) {
      try {
        const savedPreferences = JSON.parse(decodeURIComponent(savedConsent));
        setCookiePreferences(savedPreferences);
        applyPreferences(savedPreferences);
      } catch (error) {
        console.error("Error parsing cookie consent:", error);
        // If there's an error, treat it as no consent
        handleReject();
      }
    }
  }, []);

  // Function to apply cookie preferences
  const applyPreferences = (preferences) => {
    // Set the consent preferences in a cookie
    setCookie(
      "cookieConsent",
      encodeURIComponent(JSON.stringify(preferences)),
      365
    );

    // Essential cookies are always enabled
    setCookie("essential", "true", 365);

    // Handle analytics cookies
    if (preferences.analytics) {
      manageAnalytics(true);
    } else {
      manageAnalytics(false);
    }

    // Handle preference cookies
    if (preferences.preferences) {
      setCookie("preferences", "true", 365);
    } else {
      setCookie("preferences", "", -1);
    }
  };

  const handleAcceptAll = () => {
    const allAccepted = {
      essential: true,
      analytics: true,
      preferences: true,
    };
    setCookiePreferences(allAccepted);
    applyPreferences(allAccepted);
    onClose();
  };

  const handleReject = () => {
    const essentialOnly = {
      essential: true,
      analytics: false,
      preferences: false,
    };
    setCookiePreferences(essentialOnly);
    applyPreferences(essentialOnly);
    onClose();
  };

  const handleSavePreferences = () => {
    applyPreferences(cookiePreferences);
    onClose();
  };

  const handleTogglePreference = (key) => {
    if (key === "essential") return; // Essential cookies cannot be disabled
    setCookiePreferences((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-[100] flex items-center justify-center p-4 print:hidden">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-2xl overflow-hidden">
        <div className="p-6">
          <h2 className="minecraft-subtitle text-lg md:text-2xl mb-4">
            Cookie Settings
          </h2>

          <div className="text-gray-600 mb-6 text-xs md:text-base">
            This website uses cookies to enhance your experience. We require
            essential cookies for basic functionality.
            <Link
              to="/cookie-policy"
              className="text-green-600 hover:text-green-700 underline ml-2"
            >
              Learn more
            </Link>
          </div>

          <div className="space-y-6 mb-6">
            <div className="bg-gray-50 p-4 rounded">
              <div className="flex items-center justify-between mb-2">
                <div>
                  <div className="font-bold text-sm md:text-xl">
                    Essential Cookies
                  </div>
                  <div className="text-xs md:text-sm text-gray-600">
                    Required for the website to function properly
                  </div>
                </div>
                <input
                  type="checkbox"
                  checked={cookiePreferences.essential}
                  disabled
                  className="minecraft-checkbox"
                />
              </div>
            </div>

            <div className="bg-gray-50 p-4 rounded">
              <div className="flex items-center justify-between mb-2">
                <div>
                  <div className="font-bold text-sm md:text-xl">
                    Analytics Cookies
                  </div>
                  <div className="text-xs md:text-sm text-gray-600">
                    Help us understand how visitors use our site
                  </div>
                </div>
                <input
                  type="checkbox"
                  checked={cookiePreferences.analytics}
                  onChange={() => handleTogglePreference("analytics")}
                  className="minecraft-checkbox cursor-pointer"
                />
              </div>
            </div>

            <div className="bg-gray-50 p-4 rounded">
              <div className="flex items-center justify-between mb-2">
                <div>
                  <div className="font-bold text-sm md:text-xl">
                    Preference Cookies
                  </div>
                  <div className="text-xs md:text-sm text-gray-600">
                    Remember your settings and preferences
                  </div>
                </div>
                <input
                  type="checkbox"
                  checked={cookiePreferences.preferences}
                  onChange={() => handleTogglePreference("preferences")}
                  className="minecraft-checkbox cursor-pointer"
                />
              </div>
            </div>
          </div>

          <div className="flex flex-wrap gap-2 justify-end border-t pt-4">
            <button
              onClick={handleReject}
              className="minecraft-btn text-xs md:text-base bg-red-600 hover:bg-red-700"
            >
              Reject All
            </button>

            <button
              onClick={handleAcceptAll}
              className="minecraft-btn bg-green-600 hover:bg-green-700 text-xs md:text-base"
            >
              Accept All
            </button>
            <button
              onClick={handleSavePreferences}
              className="minecraft-btn bg-gray-600 hover:bg-gray-700 text-xs md:text-base"
            >
              Save Preferences
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CookieBanner;
