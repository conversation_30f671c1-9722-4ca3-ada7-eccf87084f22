import { Link, useLocation } from "react-router-dom";

function Navigation({ isOpen, setIsOpen }) {
  const location = useLocation();
  const isHome = location.pathname === "/";

  const handleCookieSettings = () => {
    window.dispatchEvent(new Event("openCookieSettings"));
    setIsOpen(false);
  };

  return (
    <div className="print:hidden">
      {/* Menu Button - Styled exactly like sidebar button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`md:hidden fixed top-4 ${
          isHome ? "right-16" : "right-4"
        } z-[100] p-2 min-w-[40px] bg-minecraft-button border-minecraft hover:bg-minecraft-button-hover cursor-pointer text-white font-bold shadow-minecraft print:hidden`}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="square"
          strokeLinejoin="round"
          className="w-6 h-6"
        >
          <path d="M4 6h16M4 12h16M4 18h16" />
          <rect x="2" y="5" width="2" height="2" fill="currentColor" />
          <rect x="2" y="11" width="2" height="2" fill="currentColor" />
          <rect x="2" y="17" width="2" height="2" fill="currentColor" />
        </svg>
      </button>

      {/* Overlay */}
      <div
        className={`fixed inset-0 bg-black/50 transition-opacity duration-300 
          md:hidden z-[90] ${
            isOpen
              ? "opacity-100 pointer-events-auto"
              : "opacity-0 pointer-events-none"
          } print:hidden`}
        onClick={() => setIsOpen(false)}
      ></div>

      {/* Navigation Menu - Styled like footer */}
      <nav
        className={`minecraft-footer print:hidden fixed md:static inset-y-0 right-0 
          w-64 md:w-full z-[95] transform transition-transform duration-300 ease-in-out
          ${isOpen ? "translate-x-0" : "translate-x-full md:translate-x-0"}`}
      >
        <div className="w-full mx-auto px-4 py-4">
          {/* Mobile-only close button and title */}
          <div className="flex justify-between items-center mb-4 md:hidden">
            <h2 className="minecraft-subtitle text-lg">Menu</h2>
            <button
              onClick={() => setIsOpen(false)}
              className="text-gray-600 hover:text-gray-800"
            >
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          {/* Main Navigation Links */}
          <nav className="space-y-2 md:space-y-0 md:flex md:justify-center md:items-center md:space-x-8">
            <Link
              to="/"
              onClick={() => setIsOpen(false)}
              className={`block hover:text-gray-300 transition-colors ${
                isHome ? "text-gray-300" : ""
              }`}
            >
              Home
            </Link>
            <Link
              to="/why-minecraft"
              onClick={() => setIsOpen(false)}
              className={`block hover:text-gray-300 transition-colors ${
                location.pathname === "/why-minecraft" ? "text-gray-300" : ""
              }`}
            >
              Why Minecraft?
            </Link>
            <Link
              to="/papercraft-benefits"
              onClick={() => setIsOpen(false)}
              className={`block hover:text-gray-300 transition-colors ${
                location.pathname === "/papercraft-benefits"
                  ? "text-gray-300"
                  : ""
              }`}
            >
              Papercraft Benefits
            </Link>
            <Link
              to="/blog"
              onClick={() => setIsOpen(false)}
              className={`block hover:text-gray-300 transition-colors ${
                location.pathname === "/blog" ? "text-gray-300" : ""
              }`}
            >
              Blog
            </Link>
            <Link
              to="/contact"
              onClick={() => setIsOpen(false)}
              className={`block hover:text-gray-300 transition-colors ${
                location.pathname === "/contact" ? "text-gray-300" : ""
              }`}
            >
              Contact
            </Link>
          </nav>

          {/* Legal and Settings Section - Mobile Only */}
          <div className="pt-4 mt-4 border-t border-gray-600 md:hidden">
            <h3 className="text-sm font-bold mb-2">Legal & Settings</h3>
            <div className="space-y-2">
              <button
                onClick={handleCookieSettings}
                className="block w-full text-left hover:text-gray-300 transition-colors text-sm"
              >
                Cookie Settings
              </button>
              <Link
                to="/privacy-policy"
                className="block hover:text-gray-300 transition-colors text-sm"
                onClick={() => setIsOpen(false)}
              >
                Privacy Policy
              </Link>
              <Link
                to="/cookie-policy"
                className="block hover:text-gray-300 transition-colors text-sm"
                onClick={() => setIsOpen(false)}
              >
                Cookie Policy
              </Link>
            </div>
          </div>
        </div>
      </nav>
    </div>
  );
}

export default Navigation;
