import { Link } from "react-router-dom";
import PageLayout from "../components/PageLayout";
import { Helmet } from "react-helmet";
import PropTypes from "prop-types";

// Section component for consistent styling
export const ArticleSection = ({ title, children }) => (
  <section className="mb-8">
    <h2 className="minecraft-subtitle text-2xl mb-4">{title}</h2>
    <div className="grid grid-cols-1 gap-4">{children}</div>
  </section>
);

// Related Articles component
const RelatedArticles = ({ currentPath, title }) => {
  // Import articles data from Blog.jsx
  const articles = [
    {
      title: "Top 10 Minecraft Crafting Materials for Kids",
      description:
        "Discover the best materials for creating Minecraft papercraft with kids. From card stock to scissors, learn what supplies you need for successful crafting sessions.",
      path: "/blog/top-materials-for-kids",
      date: "January 25, 2024",
      imageUrl: "/images/blog/Top-10-Minecraft-Crafting-Materials-for-Kids.png",
    },
    {
      title: "How to Make Minecraft Decorations for Parties",
      description:
        "Transform your party space into a Minecraft wonderland with these creative papercraft decorations. Perfect for birthdays and celebrations!",
      path: "/blog/minecraft-party-decorations",
      date: "January 26, 2024",
      imageUrl:
        "/images/blog/How-to-Make-Minecraft-Decorations-for-Parties.png",
    },
    {
      title: "Educational Benefits of Minecraft Paper Crafting",
      description:
        "Learn how Minecraft papercraft can enhance learning, develop motor skills, and boost creativity in children.",
      path: "/blog/educational-benefits",
      date: "January 27, 2024",
      imageUrl:
        "/images/blog/Educational-Benefits-of-Minecraft-Paper-Crafting.png",
    },
    {
      title: "Best Paper Types for Minecraft Block Templates",
      description:
        "Find out which paper types work best for different Minecraft papercraft projects and get tips for perfect prints.",
      path: "/blog/best-paper-types",
      date: "January 28, 2024",
      imageUrl:
        "/images/blog/Best-Paper-Types-for-Minecraft-Block-Templates.png",
    },
    // ... other articles
  ];

  // Get 3 random articles excluding the current one
  const getRelatedArticles = () => {
    const otherArticles = articles.filter(
      (article) => article.path !== currentPath
    );
    const shuffled = [...otherArticles].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, 3);
  };

  const relatedArticles = getRelatedArticles();

  return (
    <section>
      <h2 className="minecraft-subtitle text-2xl mb-6">More Articles</h2>
      <div className="grid grid-cols-1 cols-1 lg:grid-cols-3 gap-6">
        {relatedArticles.map((article) => (
          <Link
            key={article.path}
            to={article.path}
            className="bg-white rounded-lg shadow-lg overflow-hidden transition-transform hover:scale-105"
          >
            <img
              src={article.imageUrl}
              alt={article.title}
              className="w-full h-48 object-cover"
            />
            <div className="p-4">
              <div className="text-sm text-gray-500 mb-2">{article.date}</div>
              <h3 className="minecraft-subtitle text-lg mb-2 line-clamp-2">
                {article.title}
              </h3>
              <p className="text-gray-600 text-sm line-clamp-3">
                {article.description}
              </p>
            </div>
          </Link>
        ))}
      </div>
    </section>
  );
};

// Card component for consistent styling
export const BenefitCard = ({ title, children, emoji }) => (
  <div className="bg-white p-4 rounded-lg shadow-md">
    <h3 className="minecraft-text text-lg mb-2">
      {emoji} {title}
    </h3>
    {children}
  </div>
);

const ArticleTemplate = ({
  title,
  description,
  keywords,
  ogType,
  canonicalPath,
  structuredData,
  datePublished,
  dateModified,
  category,
  wordCount,
  readingTime,
  author,
  children,
}) => {
  const seoTitle = `${title} | PaperCrafter`;

  return (
    <>
      <Helmet>
        <title>{seoTitle}</title>
        <meta
          name="description"
          content={description}
        />
        <meta
          name="keywords"
          content={keywords}
        />
        <meta
          property="og:title"
          content={title}
        />
        <meta
          property="og:description"
          content={description}
        />
        <meta
          property="og:type"
          content={ogType}
        />
        <link
          rel="canonical"
          href={`https://papercrafter.netlify.app${canonicalPath}`}
        />
        <script type="application/ld+json">
          {JSON.stringify(structuredData)}
        </script>
      </Helmet>

      <PageLayout
        title={seoTitle}
        description={description}
        keywords={keywords}
        ogType={ogType}
        canonicalPath={canonicalPath}
        structuredData={structuredData}
        datePublished={datePublished}
        dateModified={dateModified}
        wordCount={wordCount}
        readingTime={readingTime}
        author={author}
        loadingMessage="Loading Article..."
      >
        <article className="max-w-7xl mx-auto px-4 py-8">
          <h1 className="minecraft-title text-4xl mb-2">{title}</h1>
          {category && (
            <div className="mb-8">
              <span className="inline-block bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">
                {category}
              </span>
            </div>
          )}
          <div className="container w-full mx-auto p-2">
            <div className="grid md:grid-cols-1">
              {children}
              <RelatedArticles
                currentPath={canonicalPath}
                title={title}
              />
            </div>
          </div>
        </article>
      </PageLayout>
    </>
  );
};

ArticleTemplate.propTypes = {
  title: PropTypes.string.isRequired,
  description: PropTypes.string.isRequired,
  keywords: PropTypes.string.isRequired,
  ogType: PropTypes.string.isRequired,
  canonicalPath: PropTypes.string.isRequired,
  structuredData: PropTypes.object.isRequired,
  datePublished: PropTypes.string.isRequired,
  category: PropTypes.string,
  children: PropTypes.node.isRequired,
};

ArticleSection.propTypes = {
  title: PropTypes.string.isRequired,
  children: PropTypes.node.isRequired,
};

BenefitCard.propTypes = {
  title: PropTypes.string.isRequired,
  children: PropTypes.node.isRequired,
  emoji: PropTypes.string.isRequired,
};

export default ArticleTemplate;
