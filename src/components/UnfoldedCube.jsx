import React from "react";

const SVGFlap = ({ className = "", style = {}, type, position }) => {
  // Define different flap shapes based on type
  const getPath = () => {
    switch (type) {
      case "top":
        return "M 20,0 L 80,0 L 100,100 L 0,100 Z";
      case "bottom":
        return "M 0,0 L 100,0 L 80,100 L 20,100 Z";
      case "side":
        return "M 0,20 L 100,0 L 100,100 L 0,80 Z";
      case "topSide":
      case "bottomSide":
        return "M 0,0 L 100,20 L 100,80 L 0,100 Z";
      default:
        return "M 0,0 L 100,0 L 100,100 L 0,100 Z";
    }
  };

  const getRotationStyle = () => {
    if (position === "leftF" || position === "leftB") {
      return { transform: "rotate(180deg)" };
    }
    if (position === "rightF" || position === "rightB") {
      return { transform: "rotate(0deg)" };
    }
    return {};
  };

  const getAlignmentClass = () => {
    if (position === "leftF" || position === "leftB") {
      return "justify-end";
    }
    if (position === "rightF" || position === "rightB") {
      return "justify-start";
    }
    return "justify-center";
  };

  const isSideFlap = type === "topSide" || type === "bottomSide";

  return (
    <div
      className={`w-full h-full flex items-center ${getAlignmentClass()} ${className}`}
      style={style}
    >
      <svg
        viewBox="0 0 100 100"
        preserveAspectRatio="none"
        className={isSideFlap ? "h-full w-[30%]" : "w-full h-full"}
        style={getRotationStyle()}
      >
        <path
          d={getPath()}
          className="fill-gray-200 stroke-black print:stroke-gray-800"
          strokeWidth="1"
        />
      </svg>
    </div>
  );
};

const UnfoldedCube = ({
  faces,
  onFaceClick,
  onClearFace,
  size = 100,
  rotation = 0,
}) => {
  const Face = ({ face, gridArea }) => (
    console.log(face),
    (
      <div
        onClick={() => onFaceClick(face)}
        className={`w-full h-full border border-black cursor-pointer hover:border-blue-500 print:border-0  print:border-dashed print:border-gray-800/25
        ${face == "front" ? "print:border" : ""}
        ${
          face == "right"
            ? "print:border-r print:border-l-0 print:border-t-0 print:border-b-0"
            : ""
        }
        ${faces[face] ? "" : "bg-gray-200"}`}
        style={{
          gridArea,
        }}
      >
        {faces[face] && (
          <div
            className="w-full h-full relative group"
            style={{ imageRendering: "pixelated" }}
          >
            <img
              src={`/blocks/${faces[face]}.png`}
              alt={faces[face]}
              className="w-full h-full"
              style={{ imageRendering: "pixelated" }}
            />
            <button
              onClick={(e) => {
                e.stopPropagation();
                onClearFace(face);
              }}
              className="absolute top-1 right-1 w-5 h-5 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 print:hidden flex items-center justify-center text-xs"
              title="Clear face"
            >
              ×
            </button>
          </div>
        )}
      </div>
    )
  );

  const flapSize = size * 0.3;

  return (
    <div
      className="relative mx-auto p-4 "
      style={{
        display: "grid",
        gridTemplateAreas: `
          ".    .     topF   .     .    ."
          ".    leftF top    rightF .    ."
          "leftL left front  right  back ."
          ".    leftB bottom rightB .    ."
          ".    .     bottomF .     .    ."
        `,
        gridTemplateColumns: `${flapSize}px ${size}px ${size}px ${size}px ${size}px ${flapSize}px`,
        gridTemplateRows: `${flapSize}px ${size}px ${size}px ${size}px ${flapSize}px`,
        gap: "0px",
      }}
    >
      {/* Main faces */}
      <Face face="top" gridArea="top" />
      <Face face="left" gridArea="left" />
      <Face face="front" gridArea="front" />
      <Face face="right" gridArea="right" />
      <Face face="back" gridArea="back" />
      <Face face="bottom" gridArea="bottom" />

      {/* Top face flaps */}
      <SVGFlap
        style={{ gridArea: "topF" }}
        className="w-full h-full"
        type="top"
      />
      <SVGFlap
        style={{ gridArea: "leftF" }}
        className="w-full h-full"
        type="topSide"
        position="leftF"
      />
      <SVGFlap
        style={{ gridArea: "rightF" }}
        className="w-full h-full"
        type="topSide"
        position="rightF"
      />

      {/* Left face flap */}
      <SVGFlap
        style={{ gridArea: "leftL" }}
        className="w-full h-full"
        type="side"
      />

      {/* Bottom face flaps */}
      <SVGFlap
        style={{ gridArea: "leftB" }}
        className="w-full h-full"
        type="bottomSide"
        position="leftB"
      />
      <SVGFlap
        style={{ gridArea: "rightB" }}
        className="w-full h-full"
        type="bottomSide"
        position="rightB"
      />
      <SVGFlap
        style={{ gridArea: "bottomF" }}
        className="w-full h-full"
        type="bottom"
      />
    </div>
  );
};

export default UnfoldedCube;
