import React, { useState } from 'react';

function Accordion({ title, children }) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="w-full print:hidden">
      <button
        className="w-full flex justify-between items-center p-4 minecraft-panel"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="minecraft-subtitle text-lg">{title}</span>
        <span className="transform transition-transform duration-200" style={{ 
          transform: isOpen ? 'rotate(180deg)' : 'rotate(0deg)'
        }}>
          ▼
        </span>
      </button>
      {isOpen && (
        <div className="minecraft-panel mt-2 p-4 animate-slideDown">
          {children}
        </div>
      )}
    </div>
  );
}

export default Accordion;
