import { Routes, Route } from "react-router-dom";
import Home from "../pages/Home";
import Contact from "../pages/Contact";
import Benefits from "../pages/Benefits";
import Blog from "../pages/Blog";
import WhyMinecraft from "../pages/WhyMinecraft";
import PrivacyPolicy from "../pages/PrivacyPolicy";
import CookiePolicy from "../pages/CookiePolicy";
import Article from "../pages/Article";

const AppRoutes = () => {
  return (
    <Routes>
      <Route path="/" element={<Home />} />
      <Route path="/contact" element={<Contact />} />
      <Route path="/blog" element={<Blog />} />
      <Route path="/blog/:articleId" element={<Article />} />
      <Route path="/privacy-policy" element={<PrivacyPolicy />} />
      <Route path="/cookie-policy" element={<CookiePolicy />} />
      <Route path="/why-minecraft" element={<WhyMinecraft />} />
      <Route path="/papercraft-benefits" element={<Benefits />} />
    </Routes>
  );
};

export default AppRoutes;
