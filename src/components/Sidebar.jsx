import React from "react";
import BlockTextures from "./BlockTexures";

function Sidebar({
  blocks,
  selectedTexture,
  handleTextureSelect,
  searchQuery,
  setSearchQuery,
  isOpen,
  setIsOpen,
}) {
  return (
    <>
      {/* Hamburger Menu Button - Only visible on mobile */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="print:hidden md:hidden fixed top-4 right-4 z-[100] p-2 min-w-[40px]
          bg-minecraft-button border-minecraft hover:bg-minecraft-button-hover
          cursor-pointer text-white font-bold shadow-minecraft">
        {/* Texture Selection Icon */}
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="square"
          strokeLinejoin="round"
          className="w-6 h-6">
          {/* Grid pattern to represent textures */}
          <rect x="3" y="3" width="7" height="7" />
          <rect x="14" y="3" width="7" height="7" />
          <rect x="3" y="14" width="7" height="7" />
          <rect x="14" y="14" width="7" height="7" />
          {/* Pixel details to make it more Minecraft-like */}
          <rect x="5" y="5" width="3" height="3" fill="currentColor" />
          <rect x="16" y="5" width="3" height="3" fill="currentColor" />
          <rect x="5" y="16" width="3" height="3" fill="currentColor" />
          <rect x="16" y="16" width="3" height="3" fill="currentColor" />
        </svg>
      </button>

      {/* Overlay for mobile */}
      <div
        className={`fixed inset-0 bg-black/50 transition-opacity duration-300 
          md:hidden z-[90] ${
            isOpen
              ? "opacity-100 pointer-events-auto"
              : "opacity-0 pointer-events-none"
          } print:hidden`}
        onClick={() => setIsOpen(false)}></div>

      {/* Sidebar */}
      <div
        className={`print:hidden fixed md:static inset-y-0 right-0 
          sm:w-3/4 md:w-2/3 z-[95] transform transition-transform 
          duration-300 ease-in-out ${
            isOpen ? "translate-x-0" : "translate-x-full md:translate-x-0"
          }`}>
        <div className="minecraft-panel h-full md:h-[calc(100vh)] flex flex-col">
          <h2 className="minecraft-title text-xl mb-4">Block Textures</h2>

          {/* Search Box */}
          <div className="mb-4">
            <input
              type="text"
              placeholder="Search blocks..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="minecraft-input w-full px-4 py-2 rounded"
            />
          </div>

          {/* Texture Grid */}
          <div className="flex-1 min-h-0 overflow-hidden">
            <BlockTextures
              blocks={blocks}
              selectedTexture={selectedTexture}
              handleTextureSelect={(texture) => {
                handleTextureSelect(texture);
                if (window.innerWidth < 768) {
                  // Close sidebar on mobile after selection
                  setIsOpen(false);
                }
              }}
              searchQuery={searchQuery}
            />
          </div>
        </div>
      </div>
    </>
  );
}

export default Sidebar;
