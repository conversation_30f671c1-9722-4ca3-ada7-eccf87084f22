import { useState, useEffect } from "react";
import { Helmet } from "react-helmet-async";

const SEO_CONFIG = {
  baseUrl: "https://papercrafter.netlify.app",
  defaultImage: "/images/social-preview.png",
  siteName: "PaperCrafter",
  twitterHandle: "@PaperCrafter",
};

const PageLayout = ({
  children,
  title,
  description,
  keywords,
  ogType = "website",
  canonicalPath,
  structuredData,
  loadingMessage = "Loading...",
}) => {
  // Add validation for required props
  if (!title || !description || !canonicalPath) {
    console.error("Missing required SEO props");
  }

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const loadContent = async () => {
      try {
        // Simulate content loading with a small delay
        await new Promise((resolve) => setTimeout(resolve, 100));
        setIsLoading(false);
      } catch (err) {
        setError(err);
        setIsLoading(false);
      }
    };

    loadContent();
  }, []);

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8 w-full  text-center">
        <h1 className="minecraft-title text-4xl mb-8 text-red-600">
          Oops! Something went wrong
        </h1>
        <p className="mb-4">
          We're having trouble loading this page. Please try again later.
        </p>
        <button
          onClick={() => window.location.reload()}
          className="bg-green-500 text-white px-6 py-2 rounded-lg hover:bg-green-600 transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8 w-full 4 text-center">
        <h1 className="minecraft-title text-4xl mb-8">{loadingMessage}</h1>
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-gray-200 rounded w-3/4 mx-auto"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 mx-auto"></div>
        </div>
      </div>
    );
  }

  const baseUrl = SEO_CONFIG.baseUrl;
  const canonicalUrl = `${baseUrl}${canonicalPath}`;
  const ogImage = `${baseUrl}${SEO_CONFIG.defaultImage}`;

  return (
    <>
      <Helmet>
        <title>{title}</title>
        <meta
          name="description"
          content={description}
        />
        {keywords && (
          <meta
            name="keywords"
            content={keywords}
          />
        )}
        <link
          rel="canonical"
          href={canonicalUrl}
        />
        <meta
          name="robots"
          content="index, follow"
        />

        {/* Open Graph */}
        <meta
          property="og:type"
          content={ogType}
        />
        <meta
          property="og:url"
          content={canonicalUrl}
        />
        <meta
          property="og:title"
          content={title}
        />
        <meta
          property="og:description"
          content={description}
        />
        <meta
          property="og:image"
          content={ogImage}
        />
        <meta
          property="og:site_name"
          content={SEO_CONFIG.siteName}
        />

        {/* Twitter Cards */}
        <meta
          name="twitter:card"
          content="summary_large_image"
        />
        <meta
          name="twitter:site"
          content={SEO_CONFIG.twitterHandle}
        />
        <meta
          name="twitter:title"
          content={title}
        />
        <meta
          name="twitter:description"
          content={description}
        />
        <meta
          name="twitter:image"
          content={ogImage}
        />

        {/* Structured Data */}
        {structuredData && (
          <script type="application/ld+json">
            {JSON.stringify(structuredData)}
          </script>
        )}
      </Helmet>
      <div className="min-h-fit w-full md:w-3/4 lg:3/4 bg-gray-200 mx-auto">
        {children}
      </div>
    </>
  );
};

export default PageLayout;
