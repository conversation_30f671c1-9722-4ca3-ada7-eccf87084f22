import { Link } from "react-router-dom";
import CustomIcon from "./CustomIcon";

function Footer() {
  const handleCookieSettings = () => {
    window.dispatchEvent(new Event("openCookieSettings"));
  };

  return (
    <footer className="minecraft-footer py-8 print:hidden">
      <div className="container mx-auto px-4 flex flex-col lg:flex-row justify-between print:justify-start print:px-0 gap-8">
        {/* Logo and Made with Love Section */}
        <div className="space-y-4 w-full order-last lg:order-first">
          <h4 className="minecraft-text text-sm">
            Made with <CustomIcon image="/images/pickaxe.png" size={16} /> by
            Manu
            <br />
            <a
              href="mailto:<EMAIL>"
              className="hover:text-gray-300 transition-colors"
            >
              <EMAIL>
            </a>
          </h4>
          {/* Legal Links */}
          <div className="pt-4 space-y-2">
            <button
              onClick={handleCookieSettings}
              className="block hover:text-gray-300 transition-colors text-sm"
            >
              <PERSON><PERSON>s
            </button>
            <Link
              to="/privacy-policy"
              className="block hover:text-gray-300 transition-colors text-sm"
            >
              Privacy Policy
            </Link>
            <Link
              to="/cookie-policy"
              className="block hover:text-gray-300 transition-colors text-sm"
            >
              Cookie Policy
            </Link>
          </div>
        </div>

        {/* Navigation Links */}
        <div className="space-y-4 w-full">
          <h3 className="minecraft-subtitle text-lg mb-4">Quick Links</h3>
          <nav className="space-y-2">
            <Link
              to="/"
              className="block hover:text-gray-300 transition-colors"
            >
              Home
            </Link>
            <Link
              to="/blog"
              className="block hover:text-gray-300 transition-colors"
            >
              Blog
            </Link>
            <Link
              to="/why-minecraft"
              className="block hover:text-gray-300 transition-colors"
            >
              Why Minecraft?
            </Link>
            <Link
              to="/papercraft-benefits"
              className="block hover:text-gray-300 transition-colors"
            >
              Papercraft Benefits
            </Link>
            <Link
              to="/contact"
              className="block hover:text-gray-300 transition-colors"
            >
              Contact
            </Link>
          </nav>
        </div>

        {/* Resources Section */}
        <div className="space-y-4 w-full">
          <h3 className="minecraft-subtitle text-lg mb-4">Popular Articles</h3>
          <nav className="space-y-2">
            <Link
              to="/blog/beginner-projects"
              className="block hover:text-gray-300 transition-colors"
            >
              Beginner's Guide
            </Link>
            <Link
              to="/blog/best-paper-types"
              className="block hover:text-gray-300 transition-colors"
            >
              Best Paper Types
            </Link>
            <Link
              to="/blog/educational-benefits"
              className="block hover:text-gray-300 transition-colors"
            >
              Educational Benefits
            </Link>
            <Link
              to="/blog/storage-solutions"
              className="block hover:text-gray-300 transition-colors"
            >
              Storage Solutions
            </Link>
          </nav>
        </div>

        {/* Disclaimer */}
        <div className="space-y-4 w-full">
          <h3 className="minecraft-subtitle text-lg mb-4">Disclaimer</h3>
          <p className="text-sm">
            This is a fan-made website and is not affiliated with, endorsed by,
            or related to Mojang AB, Microsoft, or Minecraft. Minecraft is a
            trademark of Mojang Synergies AB.
          </p>
        </div>
      </div>

      {/* Print Footer - Hidden by default, shown only when printing */}
      <h4 className="hidden print:block minecraft-footer-print text-sm">
        made with <CustomIcon image="/images/pickaxe.png" size={16} /> by Manu
        <br />
        <a href="mailto:<EMAIL>">
          <EMAIL>
        </a>
      </h4>
    </footer>
  );
}

export default Footer;
