import { Helmet } from "react-helmet-async";

const Breadcrumbs = ({ items }) => {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement: items.map((item, index) => ({
      "@type": "ListItem",
      position: index + 1,
      name: item.name,
      item: `https://papercrafter.netlify.app${item.path}`,
    })),
  };

  return (
    <>
      <Helmet>
        <script type="application/ld+json">
          {JSON.stringify(structuredData)}
        </script>
      </Helmet>
      <nav className="text-sm mb-4">
        {items.map((item, index) => (
          <span key={index}>
            {index > 0 && " > "}
            <a
              href={item.path}
              className="text-blue-600 hover:underline"
            >
              {item.name}
            </a>
          </span>
        ))}
      </nav>
    </>
  );
};
