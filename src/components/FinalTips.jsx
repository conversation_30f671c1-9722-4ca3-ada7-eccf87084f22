import React from "react";
import { Link } from "react-router-dom";

function FinalTips(props) {
  const { title, text } = props;
  return (
    <>
      <div className="mt-8 mb-8 p-6 bg-white rounded-lg shadow-lg">
        <h2 className="minecraft-subtitle text-2xl mb-4">{title}</h2>
        <p className="mb-4">{text}</p>
        <Link
          to="/blog"
          className="inline-block bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors"
        >
          Back to Blog
        </Link>
      </div>
    </>
  );
}

export default FinalTips;
