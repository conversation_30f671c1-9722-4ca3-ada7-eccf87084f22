import React from 'react';

// SVG Flap component that can be reused for each face
const CubeFlap = ({ className = '', ...props }) => (
  <svg
    className={`cube-flap ${className}`}
    viewBox="0 0 100 100"
    preserveAspectRatio="none"
    {...props}
  >
    <path
      d="M 0,0 L 100,0 L 100,80 L 90,90 L 80,100 L 20,100 L 10,90 L 0,80 Z"
      fill="currentColor"
      strokeWidth="2"
      stroke="currentColor"
    />
  </svg>
);

// Single cube component with SVG flaps
const Cube = ({ size = 100, color = '#8B4513' }) => {
  return (
    <div
      className="cube-container relative"
      style={{
        width: size,
        height: size,
        perspective: '1000px',
      }}
    >
      <div
        className="cube relative transform-style-preserve-3d transition-transform duration-500 hover:rotate-y-180"
        style={{ width: '100%', height: '100%' }}
      >
        {/* Front face */}
        <div
          className="cube-face absolute w-full h-full bg-brown-600"
          style={{
            transform: 'translateZ(50px)',
            backgroundColor: color,
          }}
        >
          <CubeFlap className="absolute -top-1/4 w-full h-1/4 origin-bottom" />
          <CubeFlap className="absolute -bottom-1/4 w-full h-1/4 origin-top rotate-180" />
          <CubeFlap className="absolute -left-1/4 w-1/4 h-full origin-right rotate-270" />
          <CubeFlap className="absolute -right-1/4 w-1/4 h-full origin-left rotate-90" />
        </div>

        {/* Back face */}
        <div
          className="cube-face absolute w-full h-full bg-brown-700"
          style={{
            transform: 'translateZ(-50px) rotateY(180deg)',
            backgroundColor: color,
          }}
        >
          <CubeFlap className="absolute -top-1/4 w-full h-1/4 origin-bottom" />
          <CubeFlap className="absolute -bottom-1/4 w-full h-1/4 origin-top rotate-180" />
          <CubeFlap className="absolute -left-1/4 w-1/4 h-full origin-right rotate-270" />
          <CubeFlap className="absolute -right-1/4 w-1/4 h-full origin-left rotate-90" />
        </div>

        {/* Top face */}
        <div
          className="cube-face absolute w-full h-full bg-brown-500"
          style={{
            transform: 'translateY(-50px) rotateX(90deg)',
            backgroundColor: color,
          }}
        />

        {/* Bottom face */}
        <div
          className="cube-face absolute w-full h-full bg-brown-500"
          style={{
            transform: 'translateY(50px) rotateX(-90deg)',
            backgroundColor: color,
          }}
        />

        {/* Left face */}
        <div
          className="cube-face absolute w-full h-full bg-brown-800"
          style={{
            transform: 'translateX(-50px) rotateY(-90deg)',
            backgroundColor: color,
          }}
        />

        {/* Right face */}
        <div
          className="cube-face absolute w-full h-full bg-brown-800"
          style={{
            transform: 'translateX(50px) rotateY(90deg)',
            backgroundColor: color,
          }}
        />
      </div>
    </div>
  );
};

// Grid component that arranges cubes in a responsive grid
const CubeGrid = ({ rows = 3, cols = 4, cubeSize = 100, colors = [] }) => {
  const defaultColors = [
    '#8B4513', // Brown
    '#556B2F', // Dark Olive Green
    '#A0522D', // Sienna
    '#6B4423', // Saddle Brown
    '#8B7355', // Light Brown
    '#CD853F', // Peru
  ];

  const getColor = (index) => {
    if (colors.length > 0) {
      return colors[index % colors.length];
    }
    return defaultColors[index % defaultColors.length];
  };

  return (
    <div className="cube-grid w-full p-4">
      <div
        className="grid gap-8"
        style={{
          gridTemplateColumns: `repeat(${cols}, minmax(${cubeSize}px, 1fr))`,
          gridTemplateRows: `repeat(${rows}, ${cubeSize}px)`,
        }}
      >
        {Array.from({ length: rows * cols }).map((_, index) => (
          <Cube key={index} size={cubeSize} color={getColor(index)} />
        ))}
      </div>
    </div>
  );
};

export { CubeGrid, Cube, CubeFlap };
