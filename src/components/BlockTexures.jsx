import React, { useMemo } from "react";

function BlockTextures({
  blocks,
  selectedTexture,
  handleTextureSelect,
  searchQuery,
}) {
  // Filter blocks based on search query
  const filteredBlocks = useMemo(() => {
    const query = searchQuery.toLowerCase();
    return blocks.filter((block) => block.toLowerCase().includes(query));
  }, [blocks, searchQuery]);

  return (
    <div className="h-full overflow-y-auto minecraft-scrollbar p-2">
      <div className="flex flex-wrap gap-2 content-start">
        {filteredBlocks.map((block) => (
          <button
            key={block}
            onClick={() => handleTextureSelect(block)}
            className={`group flex items-center justify-center w-[84px] h-[84px] p-2 
              bg-minecraft-button hover:bg-minecraft-button-hover transition-colors rounded 
              border-2 ${
                selectedTexture === block
                  ? "border-minecraft-selected ring-2 ring-minecraft-selected"
                  : "border-minecraft"
              }`}>
            <img
              src={`/blocks/${block}.png`}
              alt={block}
              className="w-16 h-16 object-contain group-hover:scale-105 transition-transform"
              style={{ imageRendering: "pixelated" }}
              title={block.replace(/_/g, " ")}
            />
          </button>
        ))}
      </div>
    </div>
  );
}

export default BlockTextures;
